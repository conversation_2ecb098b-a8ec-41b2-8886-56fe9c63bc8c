# 环境监测数据自动采集器 - 简化版使用说明

## 功能简化说明

根据您的反馈，已将插件简化，**完全移除了时间配置功能**。现在插件的工作方式：

### ✅ 插件功能
- **自动遍历排口类型、区域、企业、监测项目**
- **检测异常数据并自动截图**
- **生成采集报告**

### ❌ 插件不再处理
- ~~时间控件检测~~
- ~~时间范围设置~~
- ~~小时选择操作~~

## 使用流程

### 第一步：手动设置时间参数
在环境监测系统页面上，**您需要手动设置**：
- 小时选择
- 开始时间
- 结束时间

### 第二步：配置插件参数
点击插件图标，配置以下选项：

#### 查询配置
- ✓ 自动遍历所有排口类型
- ✓ 自动遍历所有区域
- ✓ 自动遍历所有企业
- ✓ 自动遍历所有监测项目

#### 异常检测规则
- 数值异常阈值：超过此值标记异常
- ✓ 标记空值或无效数据
- ✓ 自动检测超标数据

#### 保存设置
- 选择截图保存文件夹
- ✓ 发现异常时自动截图

### 第三步：开始采集
点击"开始采集"按钮，插件将：

1. **保持您设置的时间参数不变**
2. **自动遍历所有选中的参数组合**：
   - 遍历所有排口类型
   - 对每个排口类型，遍历所有区域
   - 对每个区域，遍历所有企业
   - 对每个企业，遍历所有监测项目
3. **对每个组合执行查询**
4. **检测异常数据并截图保存**

## 工作原理

```
用户手动设置时间 → 插件遍历其他参数 → 自动查询 → 检测异常 → 截图保存
```

### 详细流程：
1. **用户操作**：在页面上设置小时、开始时间、结束时间
2. **插件接管**：自动遍历排口类型、区域、企业、监测项目的所有组合
3. **自动查询**：对每个参数组合执行查询操作
4. **数据分析**：收集查询结果并检测异常数据
5. **自动截图**：发现异常时自动截图并保存到指定文件夹
6. **生成报告**：完成后生成详细的采集报告

## 界面更新

插件界面现在显示：

### 使用说明区域
```
📋 请先在网页上手动设置好时间参数：
• 设置小时选择
• 设置开始时间  
• 设置结束时间
⚡ 插件将自动遍历所有选中的参数组合进行查询
```

### 其他配置区域保持不变
- 查询配置
- 异常检测规则
- 保存设置
- 控制按钮

## 优势

### ✅ 简化操作
- 不再需要等待时间控件检测
- 避免了"检测中"的等待状态
- 用户完全控制时间参数

### ✅ 更可靠
- 不依赖页面时间控件的自动识别
- 避免了时间格式兼容性问题
- 减少了出错的可能性

### ✅ 更灵活
- 用户可以设置任意时间范围
- 支持各种时间格式和控件类型
- 适应不同的环境监测系统界面

## 注意事项

1. **时间设置**：请确保在启动插件前已正确设置好页面上的时间参数
2. **参数选择**：根据需要选择要遍历的参数类型（排口、区域、企业、监测项目）
3. **保存路径**：如需自动截图，请先设置好保存文件夹路径
4. **异常检测**：根据实际需求配置异常检测规则

## 示例使用场景

### 场景：监控某个时间段的所有企业数据
1. **手动设置**：在页面上设置时间范围（如：2024-07-30 08:00 到 2024-07-30 18:00）
2. **插件配置**：勾选"自动遍历所有企业"和"自动遍历所有监测项目"
3. **开始采集**：插件自动遍历所有企业和监测项目的组合
4. **结果**：获得该时间段内所有企业的监测数据，并标记异常

### 场景：检查特定区域的排放情况
1. **手动设置**：在页面上设置查询时间
2. **插件配置**：勾选"自动遍历所有区域"和"自动遍历所有排口类型"
3. **开始采集**：插件自动遍历所有区域和排口类型的组合
4. **结果**：获得所有区域的排放数据，自动截图异常情况

## 技术说明

插件现在的核心逻辑：
```javascript
// 不再处理时间设置，直接遍历其他参数
async executeTask(task) {
    // 设置下拉框选项
    if (task.outlet) await this.setDropdownValue('outlet', task.outlet.value);
    if (task.area) await this.setDropdownValue('area', task.area.value);
    if (task.company) await this.setDropdownValue('company', task.company.value);
    if (task.monitorItem) await this.setDropdownValue('monitor_item', task.monitorItem.value);
    
    // 执行查询（使用页面当前的时间设置）
    await this.performQuery();
    
    // 收集和分析数据
    const data = await this.collectTableData();
    const anomalies = this.detectAnomalies(data, task);
    
    // 异常截图
    if (anomalies.length > 0) {
        await this.takeScreenshot(task, anomalies);
    }
}
```

这样的设计让插件更加专注于参数遍历和数据分析，而时间控制完全由用户掌握，避免了复杂的时间控件识别和操作逻辑。
