// 环境监测数据采集器 - Background Script
class BackgroundService {
    constructor() {
        this.screenshots = [];
        this.reports = [];
        this.logs = [];
        
        this.init();
    }

    init() {
        // 监听来自content script和popup的消息
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
            return true; // 保持消息通道开放
        });

        // 监听插件安装
        chrome.runtime.onInstalled.addListener(() => {
            this.onInstalled();
        });

        // 监听标签页更新
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            this.onTabUpdated(tabId, changeInfo, tab);
        });
    }

    async handleMessage(request, sender, sendResponse) {
        try {
            switch (request.action) {
                case 'saveScreenshot':
                    await this.saveScreenshot(request.data, request.filename, request.anomalies);
                    sendResponse({ success: true });
                    break;

                case 'saveReport':
                    await this.saveReport(request.report);
                    sendResponse({ success: true });
                    break;

                case 'progressUpdate':
                    this.logProgress(request.progress);
                    sendResponse({ success: true });
                    break;

                case 'error':
                    this.logError(request.message, sender);
                    sendResponse({ success: true });
                    break;

                case 'getLogs':
                    sendResponse({ logs: this.logs });
                    break;

                case 'clearLogs':
                    this.logs = [];
                    sendResponse({ success: true });
                    break;

                case 'captureTab':
                    await this.captureTab(sender.tab.id, sendResponse);
                    break;

                default:
                    sendResponse({ error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Background message handling error:', error);
            sendResponse({ error: error.message });
        }
    }

    async saveScreenshot(dataUrl, filename, anomalies) {
        try {
            // 将base64数据转换为blob
            const response = await fetch(dataUrl);
            const blob = await response.blob();
            
            // 创建下载链接
            const url = URL.createObjectURL(blob);
            
            // 使用Chrome下载API
            const downloadId = await chrome.downloads.download({
                url: url,
                filename: `screenshots/${filename}`,
                saveAs: false
            });

            // 记录截图信息
            const screenshotInfo = {
                id: downloadId,
                filename: filename,
                timestamp: new Date().toISOString(),
                anomalies: anomalies,
                size: blob.size
            };

            this.screenshots.push(screenshotInfo);
            
            // 保存到存储
            await chrome.storage.local.set({
                screenshots: this.screenshots
            });

            // 记录日志
            this.log('info', `截图已保存: ${filename}`, {
                anomalyCount: anomalies.length,
                fileSize: blob.size
            });

            // 清理URL
            setTimeout(() => URL.revokeObjectURL(url), 1000);

        } catch (error) {
            console.error('Screenshot save error:', error);
            this.logError(`截图保存失败: ${error.message}`);
            throw error;
        }
    }

    async saveReport(report) {
        try {
            // 生成报告ID
            const reportId = `report_${Date.now()}`;
            report.id = reportId;

            // 保存到内存
            this.reports.push(report);

            // 保存到存储
            await chrome.storage.local.set({
                [`report_${reportId}`]: report,
                lastReport: report,
                reports: this.reports.map(r => ({
                    id: r.id,
                    timestamp: r.timestamp,
                    summary: r.summary
                }))
            });

            // 记录日志
            this.log('info', '采集报告已保存', {
                reportId: reportId,
                dataCount: report.data.length,
                anomalyCount: report.anomalies.length
            });

            // 创建下载链接保存JSON文件
            const jsonBlob = new Blob([JSON.stringify(report, null, 2)], {
                type: 'application/json'
            });
            const jsonUrl = URL.createObjectURL(jsonBlob);
            
            await chrome.downloads.download({
                url: jsonUrl,
                filename: `reports/环境监测数据报告_${new Date().toISOString().slice(0, 10)}.json`,
                saveAs: false
            });

            setTimeout(() => URL.revokeObjectURL(jsonUrl), 1000);

        } catch (error) {
            console.error('Report save error:', error);
            this.logError(`报告保存失败: ${error.message}`);
            throw error;
        }
    }

    logProgress(progress) {
        this.log('progress', '采集进度更新', progress);
    }

    logError(message, sender = null) {
        this.log('error', message, {
            sender: sender ? {
                tab: sender.tab?.id,
                url: sender.tab?.url
            } : null
        });
    }

    log(level, message, data = null) {
        const logEntry = {
            timestamp: new Date().toISOString(),
            level: level,
            message: message,
            data: data
        };

        this.logs.push(logEntry);
        
        // 限制日志数量
        if (this.logs.length > 1000) {
            this.logs = this.logs.slice(-500);
        }

        // 保存到存储
        chrome.storage.local.set({ logs: this.logs });

        // 输出到控制台
        console.log(`[${level.toUpperCase()}] ${message}`, data);
    }

    onInstalled() {
        this.log('info', '环境监测数据采集器已安装');
        
        // 创建右键菜单
        chrome.contextMenus.create({
            id: 'startCollection',
            title: '开始数据采集',
            contexts: ['page']
        });

        chrome.contextMenus.create({
            id: 'analyzeTable',
            title: '分析当前表格',
            contexts: ['selection']
        });

        // 监听右键菜单点击
        chrome.contextMenus.onClicked.addListener((info, tab) => {
            this.handleContextMenu(info, tab);
        });
    }

    async handleContextMenu(info, tab) {
        try {
            switch (info.menuItemId) {
                case 'startCollection':
                    // 打开popup或直接开始采集
                    chrome.action.openPopup();
                    break;

                case 'analyzeTable':
                    // 分析选中的表格
                    await chrome.tabs.sendMessage(tab.id, {
                        action: 'analyzeSelection',
                        selectionText: info.selectionText
                    });
                    break;
            }
        } catch (error) {
            this.logError(`右键菜单处理失败: ${error.message}`);
        }
    }

    onTabUpdated(tabId, changeInfo, tab) {
        // 当页面完全加载后，检查是否为环境监测系统
        if (changeInfo.status === 'complete' && tab.url) {
            this.checkEnvironmentSystem(tabId, tab.url);
        }
    }

    async captureTab(tabId, sendResponse) {
        try {
            const dataUrl = await chrome.tabs.captureVisibleTab(null, {
                format: 'png',
                quality: 90
            });

            sendResponse({ dataUrl: dataUrl });
        } catch (error) {
            console.error('Tab capture error:', error);
            sendResponse({ error: error.message });
        }
    }

    async checkEnvironmentSystem(tabId, url) {
        try {
            // 发送消息检查页面类型
            const response = await chrome.tabs.sendMessage(tabId, {
                action: 'getPageInfo'
            });

            if (response && response.isEnvironmentSystem) {
                // 显示页面操作图标
                chrome.action.setBadgeText({
                    text: '✓',
                    tabId: tabId
                });
                chrome.action.setBadgeBackgroundColor({
                    color: '#4CAF50',
                    tabId: tabId
                });

                this.log('info', '检测到环境监测系统页面', {
                    tabId: tabId,
                    url: url
                });
            } else {
                // 清除图标
                chrome.action.setBadgeText({
                    text: '',
                    tabId: tabId
                });
            }
        } catch (error) {
            // 页面可能还没有加载content script，忽略错误
        }
    }

    // 定期清理旧数据
    async cleanupOldData() {
        try {
            const result = await chrome.storage.local.get(null);
            const now = Date.now();
            const maxAge = 30 * 24 * 60 * 60 * 1000; // 30天

            const keysToRemove = [];
            
            Object.keys(result).forEach(key => {
                if (key.startsWith('report_')) {
                    const report = result[key];
                    if (report.timestamp) {
                        const reportTime = new Date(report.timestamp).getTime();
                        if (now - reportTime > maxAge) {
                            keysToRemove.push(key);
                        }
                    }
                }
            });

            if (keysToRemove.length > 0) {
                await chrome.storage.local.remove(keysToRemove);
                this.log('info', `清理了 ${keysToRemove.length} 个过期报告`);
            }

        } catch (error) {
            this.logError(`数据清理失败: ${error.message}`);
        }
    }

    // 获取统计信息
    async getStatistics() {
        try {
            const result = await chrome.storage.local.get(['screenshots', 'reports', 'logs']);
            
            return {
                screenshots: result.screenshots?.length || 0,
                reports: result.reports?.length || 0,
                logs: result.logs?.length || 0,
                lastActivity: this.logs.length > 0 ? this.logs[this.logs.length - 1].timestamp : null
            };
        } catch (error) {
            this.logError(`获取统计信息失败: ${error.message}`);
            return null;
        }
    }
}

// 初始化后台服务
const backgroundService = new BackgroundService();

// 定期清理数据（每天执行一次）
chrome.alarms.create('cleanup', { periodInMinutes: 24 * 60 });
chrome.alarms.onAlarm.addListener((alarm) => {
    if (alarm.name === 'cleanup') {
        backgroundService.cleanupOldData();
    }
});
