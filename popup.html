<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>环境监测数据采集器</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>环境监测数据采集</h1>
            <div class="status" id="status">
                <span class="status-indicator" id="statusIndicator"></span>
                <span id="statusText">待配置</span>
            </div>
        </header>

        <main>
            <!-- 使用说明 -->
            <div class="usage-info">
                <h3>使用说明</h3>
                <div class="info-box">
                    <p>📋 请先在网页上手动设置好时间参数：</p>
                    <ul>
                        <li>• 设置小时选择</li>
                        <li>• 设置开始时间</li>
                        <li>• 设置结束时间</li>
                    </ul>
                    <p>⚡ 插件将自动遍历所有选中的参数组合进行查询</p>
                </div>
            </div>

            <!-- 查询配置区域 -->
            <div class="query-config">
                <h3>查询配置</h3>
                <div class="config-options">
                    <label class="checkbox-label">
                        <input type="checkbox" id="autoTraverseOutlets" checked>
                        自动遍历所有排口类型
                    </label>
                    <label class="checkbox-label">
                        <input type="checkbox" id="autoTraverseAreas" checked>
                        自动遍历所有区域
                    </label>
                    <label class="checkbox-label">
                        <input type="checkbox" id="autoTraverseCompanies" checked>
                        自动遍历所有企业
                    </label>
                    <label class="checkbox-label">
                        <input type="checkbox" id="autoTraverseMonitorItems" checked>
                        自动遍历所有监测项目
                    </label>
                </div>
            </div>

            <!-- 异常检测规则 -->
            <div class="detection-rules">
                <h3>异常检测规则</h3>
                <div class="rule-item">
                    <label>数值异常阈值:</label>
                    <input type="number" id="thresholdValue" placeholder="超过此值标记异常" step="0.01">
                </div>
                <div class="rule-item">
                    <label>空值检测:</label>
                    <label class="checkbox-label">
                        <input type="checkbox" id="detectNullValues" checked>
                        标记空值或无效数据
                    </label>
                </div>
                <div class="rule-item">
                    <label>超标检测:</label>
                    <label class="checkbox-label">
                        <input type="checkbox" id="detectExceedance" checked>
                        自动检测超标数据
                    </label>
                </div>
            </div>

            <!-- 保存设置 -->
            <div class="save-config">
                <h3>保存设置</h3>
                <div class="input-group">
                    <label>保存路径:</label>
                    <input type="text" id="savePath" placeholder="选择保存截图的文件夹" readonly>
                    <button id="selectFolder" class="btn btn-small">选择文件夹</button>
                </div>
                <label class="checkbox-label">
                    <input type="checkbox" id="autoScreenshot" checked>
                    发现异常时自动截图
                </label>
            </div>

            <!-- 控制按钮 -->
            <div class="control-panel">
                <button id="startCollection" class="btn btn-primary">开始采集</button>
                <button id="stopCollection" class="btn btn-secondary" disabled>停止采集</button>
                <button id="pauseCollection" class="btn btn-outline" disabled>暂停</button>
            </div>

            <!-- 进度显示 -->
            <div class="progress-section" id="progressSection" style="display: none;">
                <h3>采集进度</h3>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                    <span class="progress-text" id="progressText">0%</span>
                </div>
                <div class="progress-details">
                    <div class="detail-item">
                        <span>当前任务:</span>
                        <span id="currentTask">准备中...</span>
                    </div>
                    <div class="detail-item">
                        <span>已处理:</span>
                        <span id="processedCount">0</span>
                    </div>
                    <div class="detail-item">
                        <span>发现异常:</span>
                        <span id="anomalyCount">0</span>
                    </div>
                    <div class="detail-item">
                        <span>截图数量:</span>
                        <span id="screenshotCount">0</span>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="actions">
                <button id="exportReport" class="btn btn-outline">导出报告</button>
                <button id="viewLogs" class="btn btn-outline">查看日志</button>
                <button id="openOptions" class="btn btn-outline">高级设置</button>
            </div>
        </main>
    </div>

    <script src="popup.js"></script>
</body>
</html>
