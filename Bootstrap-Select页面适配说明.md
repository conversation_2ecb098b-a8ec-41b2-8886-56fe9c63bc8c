# 环境监测数据采集器 - Bootstrap Select页面适配说明

## 页面结构分析

根据您提供的HTML源码，这是一个使用以下技术的环境监测系统页面：
- **Bootstrap Select** - 下拉框组件
- **EasyUI DataGrid** - 数据表格
- **jQuery** - JavaScript库
- **LayUI** - 部分UI组件

## 关键元素识别

### 下拉框元素
```html
<!-- 排口类型 -->
<select class="selectpicker" id="sel_SubType" onchange="HistoryReport.selectSubTypeChange(this.value)">

<!-- 所属地区 -->  
<select class="selectpicker" id="sel_City" onchange="HistoryReport.selectCityChange(this)">

<!-- 企业名称 -->
<select class="selectpicker" id="sel_ent" onchange="HistoryReport.selectEntChange()">

<!-- 排口名称 -->
<select class="selectpicker" id="select_sub" onchange="HistoryReport.selectSubChange()">

<!-- 监测项目 -->
<select class="selectpicker" id="sel_Item" multiple data-actions-box="true">
```

### 查询按钮
```html
<button class="btn btn-primary" onclick="HistoryReport.doSearch()">查 询</button>
```

### 数据表格
```html
<table id="dataBasic" fit="true"></table>
```

## 插件优化内容

### 1. 精确的元素识别
```javascript
// 根据具体ID识别下拉框类型
getDropdownTypeById(id) {
    switch (id) {
        case 'sel_SubType': return 'outlet';      // 排口类型
        case 'sel_City': return 'area';           // 所属地区
        case 'sel_ent': return 'company';         // 企业名称
        case 'select_sub': return 'outlet_name';  // 排口名称
        case 'sel_Item': return 'monitor_item';   // 监测项目
    }
}
```

### 2. Bootstrap Select操作
```javascript
async setBootstrapSelectValue(dropdown, value) {
    // 设置原始select值
    select.value = value;
    
    // 刷新Bootstrap Select显示
    $(select).selectpicker('val', value);
    $(select).selectpicker('refresh');
    
    // 触发相应的onchange函数
    window.HistoryReport?.selectSubTypeChange?.(value);
}
```

### 3. EasyUI DataGrid识别
```javascript
// 优先查找dataBasic表格
const dataBasicTable = document.getElementById('dataBasic');

// 备用：查找EasyUI相关表格
const easyuiTables = document.querySelectorAll('.datagrid, [class*="datagrid"]');
```

## 预期识别结果

现在插件应该能够识别：
```
Bootstrap Select: sel_SubType (outlet) - 排口类型, 选项数: X
Bootstrap Select: sel_City (area) - 所属地区, 选项数: X  
Bootstrap Select: sel_ent (company) - 企业名称, 选项数: X
Bootstrap Select: select_sub (outlet_name) - 排口名称, 选项数: X
Bootstrap Select: sel_Item (monitor_item) - 监测项目, 选项数: X
找到查询按钮: 查 询 onclick: HistoryReport.doSearch()
找到EasyUI DataGrid表格: dataBasic
```

## 使用步骤

### 第一步：重新加载插件
1. 打开 `chrome://extensions/`
2. 找到插件，点击🔄重新加载按钮

### 第二步：刷新页面
1. 刷新环境监测系统页面（F5）
2. 等待页面完全加载，确保所有下拉框都已初始化

### 第三步：测试识别
1. 打开开发者工具（F12 → Console）
2. 点击插件图标
3. 查看控制台输出，应该看到：
   ```
   Bootstrap Select: sel_SubType (outlet) - 排口类型, 选项数: X
   Bootstrap Select: sel_City (area) - 所属地区, 选项数: X
   ...
   总共识别了 5 个下拉框控件
   ```

### 第四步：配置采集参数
1. 在插件界面中配置：
   - ✓ 自动遍历所有排口类型
   - ✓ 自动遍历所有区域
   - ✓ 自动遍历所有企业
   - ✓ 自动遍历所有监测项目
2. 设置异常检测规则
3. 选择截图保存路径

### 第五步：开始采集
1. 点击"开始采集"
2. 观察控制台日志：
   ```
   设置 outlet 下拉框值为: 废水
   设置 area 下拉框值为: 某某区
   设置 company 下拉框值为: 某某企业
   执行查询操作...
   ```

## 工作流程

```
1. 识别Bootstrap Select下拉框
   ↓
2. 获取所有选项组合
   ↓  
3. 遍历每个组合：
   - 设置排口类型 → 刷新企业列表
   - 设置所属地区 → 刷新企业列表  
   - 设置企业名称 → 刷新排口列表
   - 设置排口名称 → 刷新监测项目
   - 设置监测项目
   ↓
4. 点击查询按钮
   ↓
5. 等待EasyUI DataGrid加载数据
   ↓
6. 收集表格数据并检测异常
   ↓
7. 发现异常时自动截图
```

## 特殊处理

### 1. 级联下拉框
页面中的下拉框存在级联关系：
- 排口类型 → 影响企业列表
- 所属地区 → 影响企业列表
- 企业名称 → 影响排口列表
- 排口名称 → 影响监测项目

插件会按正确顺序设置，并等待每个下拉框刷新完成。

### 2. 多选监测项目
监测项目下拉框支持多选，插件会：
- 获取所有可用的监测项目
- 可以选择遍历单个项目或全部项目

### 3. 时间参数
页面有多种时间模式（实时、分钟、小时、日、月、年），插件不会修改时间设置，用户需要手动选择合适的时间范围。

## 故障排除

### 问题1：仍然找到0个下拉框
**可能原因**：页面加载不完整或Bootstrap Select未初始化
**解决方案**：
1. 等待页面完全加载
2. 确保看到下拉框正常显示
3. 刷新页面重试

### 问题2：下拉框无法设置值
**可能原因**：Bootstrap Select版本不兼容或jQuery未加载
**解决方案**：
1. 检查控制台是否有JavaScript错误
2. 确认页面使用的Bootstrap Select版本
3. 手动测试下拉框是否正常工作

### 问题3：查询按钮点击无效
**可能原因**：HistoryReport对象未初始化
**解决方案**：
1. 等待页面JavaScript完全加载
2. 在控制台测试：`HistoryReport.doSearch()`
3. 检查是否有JavaScript错误

### 问题4：数据表格无法识别
**可能原因**：EasyUI DataGrid动态生成
**解决方案**：
1. 先手动执行一次查询
2. 等待表格显示数据后再启动插件
3. 插件会查找动态生成的表格元素

## 调试命令

在控制台中执行以下命令进行调试：

```javascript
// 检查Bootstrap Select状态
console.log('Bootstrap Select状态:');
['sel_SubType', 'sel_City', 'sel_ent', 'select_sub', 'sel_Item'].forEach(id => {
    const select = document.getElementById(id);
    console.log(`${id}:`, {
        exists: !!select,
        value: select?.value,
        options: select?.options?.length,
        bootstrapSelect: !!$(select).data('selectpicker')
    });
});

// 检查HistoryReport对象
console.log('HistoryReport对象:', typeof window.HistoryReport);

// 检查EasyUI DataGrid
console.log('DataGrid状态:', {
    element: !!document.getElementById('dataBasic'),
    easyui: !!window.$.fn.datagrid
});
```

通过这些优化，插件现在应该能够完美适配您的Bootstrap Select环境监测系统页面！
