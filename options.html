<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>环境监测数据采集器 - 高级设置</title>
    <link rel="stylesheet" href="options.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>环境监测数据采集器</h1>
            <p>高级设置和配置选项</p>
        </header>

        <nav class="tabs">
            <button class="tab-button active" data-tab="general">常规设置</button>
            <button class="tab-button" data-tab="detection">检测规则</button>
            <button class="tab-button" data-tab="storage">存储设置</button>
            <button class="tab-button" data-tab="advanced">高级选项</button>
            <button class="tab-button" data-tab="logs">日志管理</button>
        </nav>

        <main>
            <!-- 常规设置 -->
            <div class="tab-content active" id="general">
                <h2>常规设置</h2>
                
                <div class="setting-group">
                    <h3>默认时间配置</h3>
                    <div class="setting-item">
                        <label>默认查询时间范围:</label>
                        <select id="defaultTimeRange">
                            <option value="1">最近1小时</option>
                            <option value="6">最近6小时</option>
                            <option value="24">最近24小时</option>
                            <option value="168">最近7天</option>
                            <option value="720">最近30天</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label>默认查询间隔:</label>
                        <select id="defaultInterval">
                            <option value="1">1小时</option>
                            <option value="2">2小时</option>
                            <option value="4">4小时</option>
                            <option value="6">6小时</option>
                            <option value="12">12小时</option>
                            <option value="24">24小时</option>
                        </select>
                    </div>
                </div>

                <div class="setting-group">
                    <h3>自动化设置</h3>
                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="autoStart">
                            页面加载后自动开始采集
                        </label>
                    </div>
                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="autoRetry">
                            遇到错误时自动重试
                        </label>
                    </div>
                    <div class="setting-item">
                        <label>重试次数:</label>
                        <input type="number" id="retryCount" min="1" max="10" value="3">
                    </div>
                    <div class="setting-item">
                        <label>重试间隔(秒):</label>
                        <input type="number" id="retryDelay" min="1" max="60" value="5">
                    </div>
                </div>
            </div>

            <!-- 检测规则 -->
            <div class="tab-content" id="detection">
                <h2>异常检测规则</h2>
                
                <div class="setting-group">
                    <h3>数值检测</h3>
                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="enableThresholdDetection">
                            启用阈值检测
                        </label>
                    </div>
                    <div class="setting-item">
                        <label>全局阈值:</label>
                        <input type="number" id="globalThreshold" step="0.01" placeholder="超过此值标记异常">
                    </div>
                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="enableRangeDetection">
                            启用范围检测
                        </label>
                    </div>
                    <div class="setting-item">
                        <label>正常范围最小值:</label>
                        <input type="number" id="rangeMin" step="0.01">
                    </div>
                    <div class="setting-item">
                        <label>正常范围最大值:</label>
                        <input type="number" id="rangeMax" step="0.01">
                    </div>
                </div>

                <div class="setting-group">
                    <h3>文本检测</h3>
                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="enableKeywordDetection">
                            启用关键词检测
                        </label>
                    </div>
                    <div class="setting-item">
                        <label>异常关键词(每行一个):</label>
                        <textarea id="anomalyKeywords" rows="5" placeholder="超标&#10;异常&#10;故障&#10;离线&#10;无数据"></textarea>
                    </div>
                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="caseSensitive">
                            区分大小写
                        </label>
                    </div>
                </div>

                <div class="setting-group">
                    <h3>自定义规则</h3>
                    <div class="custom-rules" id="customRules">
                        <!-- 动态生成的自定义规则 -->
                    </div>
                    <button id="addCustomRule" class="btn btn-outline">添加自定义规则</button>
                </div>
            </div>

            <!-- 存储设置 -->
            <div class="tab-content" id="storage">
                <h2>存储和导出设置</h2>
                
                <div class="setting-group">
                    <h3>截图设置</h3>
                    <div class="setting-item">
                        <label>默认保存路径:</label>
                        <input type="text" id="defaultSavePath" placeholder="C:\Screenshots\" readonly>
                        <button id="selectDefaultPath" class="btn btn-small">选择</button>
                    </div>
                    <div class="setting-item">
                        <label>截图质量:</label>
                        <select id="screenshotQuality">
                            <option value="0.8">高质量</option>
                            <option value="0.6">中等质量</option>
                            <option value="0.4">低质量</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label>截图格式:</label>
                        <select id="screenshotFormat">
                            <option value="png">PNG</option>
                            <option value="jpeg">JPEG</option>
                            <option value="webp">WebP</option>
                        </select>
                    </div>
                </div>

                <div class="setting-group">
                    <h3>数据导出</h3>
                    <div class="setting-item">
                        <label>导出格式:</label>
                        <select id="exportFormat">
                            <option value="json">JSON</option>
                            <option value="csv">CSV</option>
                            <option value="excel">Excel</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="includeScreenshots">
                            导出时包含截图
                        </label>
                    </div>
                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="autoExport">
                            采集完成后自动导出
                        </label>
                    </div>
                </div>

                <div class="setting-group">
                    <h3>数据清理</h3>
                    <div class="setting-item">
                        <label>数据保留天数:</label>
                        <input type="number" id="dataRetentionDays" min="1" max="365" value="30">
                    </div>
                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="autoCleanup">
                            自动清理过期数据
                        </label>
                    </div>
                    <div class="setting-item">
                        <button id="cleanupNow" class="btn btn-outline">立即清理</button>
                        <button id="exportAll" class="btn btn-outline">导出所有数据</button>
                    </div>
                </div>
            </div>

            <!-- 高级选项 -->
            <div class="tab-content" id="advanced">
                <h2>高级选项</h2>
                
                <div class="setting-group">
                    <h3>性能设置</h3>
                    <div class="setting-item">
                        <label>并发查询数:</label>
                        <input type="number" id="concurrentQueries" min="1" max="10" value="1">
                        <small>同时执行的查询数量，过高可能导致系统不稳定</small>
                    </div>
                    <div class="setting-item">
                        <label>查询延迟(毫秒):</label>
                        <input type="number" id="queryDelay" min="100" max="10000" value="1000">
                        <small>每次查询之间的延迟时间</small>
                    </div>
                    <div class="setting-item">
                        <label>页面加载超时(秒):</label>
                        <input type="number" id="pageTimeout" min="5" max="120" value="30">
                    </div>
                </div>

                <div class="setting-group">
                    <h3>调试选项</h3>
                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="enableDebugMode">
                            启用调试模式
                        </label>
                    </div>
                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="verboseLogging">
                            详细日志记录
                        </label>
                    </div>
                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="saveDebugInfo">
                            保存调试信息
                        </label>
                    </div>
                </div>

                <div class="setting-group">
                    <h3>系统集成</h3>
                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="enableNotifications">
                            启用系统通知
                        </label>
                    </div>
                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="enableContextMenu">
                            启用右键菜单
                        </label>
                    </div>
                </div>
            </div>

            <!-- 日志管理 -->
            <div class="tab-content" id="logs">
                <h2>日志管理</h2>
                
                <div class="log-controls">
                    <button id="refreshLogs" class="btn btn-primary">刷新日志</button>
                    <button id="clearLogs" class="btn btn-secondary">清空日志</button>
                    <button id="exportLogs" class="btn btn-outline">导出日志</button>
                    <select id="logLevel">
                        <option value="all">所有级别</option>
                        <option value="error">错误</option>
                        <option value="warning">警告</option>
                        <option value="info">信息</option>
                        <option value="debug">调试</option>
                    </select>
                </div>

                <div class="log-viewer" id="logViewer">
                    <div class="log-loading">加载日志中...</div>
                </div>
            </div>
        </main>

        <footer>
            <div class="actions">
                <button id="saveSettings" class="btn btn-primary">保存设置</button>
                <button id="resetSettings" class="btn btn-secondary">重置为默认</button>
                <button id="importSettings" class="btn btn-outline">导入配置</button>
                <button id="exportSettings" class="btn btn-outline">导出配置</button>
            </div>
            <div class="version-info">
                <span>版本 1.0.0</span>
                <span>|</span>
                <a href="#" id="helpLink">帮助文档</a>
                <span>|</span>
                <a href="#" id="feedbackLink">反馈问题</a>
            </div>
        </footer>
    </div>

    <script src="options.js"></script>
</body>
</html>
