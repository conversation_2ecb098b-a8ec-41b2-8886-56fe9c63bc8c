// 环境监测数据采集器 - Popup Script
class PopupController {
    constructor() {
        this.isCollecting = false;
        this.currentTab = null;
        this.progressInterval = null;
        
        this.init();
    }

    async init() {
        // 获取当前标签页
        const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
        this.currentTab = tabs[0];
        
        // 初始化UI
        this.initializeUI();
        
        // 绑定事件
        this.bindEvents();
        
        // 加载保存的配置
        await this.loadConfig();
        
        // 获取页面信息
        await this.updatePageInfo();
        
        // 开始监听进度更新
        this.startProgressMonitoring();
    }

    initializeUI() {
        // 设置默认时间
        const now = new Date();
        const startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000); // 24小时前
        
        document.getElementById('startTime').value = this.formatDateTime(startTime);
        document.getElementById('endTime').value = this.formatDateTime(now);
        
        // 设置状态
        this.updateStatus('待配置', 'waiting');
    }

    bindEvents() {
        // 时间模式选择事件
        document.getElementById('usePageTime').addEventListener('change', () => this.onTimeModeChange());
        document.getElementById('useCustomTime').addEventListener('change', () => this.onTimeModeChange());

        // 时间配置事件
        document.getElementById('startTime').addEventListener('change', () => this.saveConfig());
        document.getElementById('endTime').addEventListener('change', () => this.saveConfig());
        document.getElementById('queryInterval').addEventListener('change', () => this.saveConfig());
        
        // 查询配置事件
        document.getElementById('autoTraverseOutlets').addEventListener('change', () => this.saveConfig());
        document.getElementById('autoTraverseAreas').addEventListener('change', () => this.saveConfig());
        document.getElementById('autoTraverseCompanies').addEventListener('change', () => this.saveConfig());
        document.getElementById('autoTraverseMonitorItems').addEventListener('change', () => this.saveConfig());
        
        // 检测规则事件
        document.getElementById('thresholdValue').addEventListener('input', () => this.saveConfig());
        document.getElementById('detectNullValues').addEventListener('change', () => this.saveConfig());
        document.getElementById('detectExceedance').addEventListener('change', () => this.saveConfig());
        
        // 保存设置事件
        document.getElementById('selectFolder').addEventListener('click', () => this.selectSaveFolder());
        document.getElementById('autoScreenshot').addEventListener('change', () => this.saveConfig());
        
        // 控制按钮事件
        document.getElementById('startCollection').addEventListener('click', () => this.startCollection());
        document.getElementById('stopCollection').addEventListener('click', () => this.stopCollection());
        document.getElementById('pauseCollection').addEventListener('click', () => this.pauseCollection());
        
        // 操作按钮事件
        document.getElementById('exportReport').addEventListener('click', () => this.exportReport());
        document.getElementById('viewLogs').addEventListener('click', () => this.viewLogs());
        document.getElementById('openOptions').addEventListener('click', () => this.openOptions());
    }

    async loadConfig() {
        try {
            const result = await chrome.storage.local.get('collectorConfig');
            if (result.collectorConfig) {
                const config = result.collectorConfig;

                // 恢复时间模式
                const timeMode = config.timeMode || 'page';
                if (timeMode === 'page') {
                    document.getElementById('usePageTime').checked = true;
                } else {
                    document.getElementById('useCustomTime').checked = true;
                }
                this.onTimeModeChange();

                // 恢复时间配置
                if (config.startTime) document.getElementById('startTime').value = config.startTime;
                if (config.endTime) document.getElementById('endTime').value = config.endTime;
                if (config.queryInterval) document.getElementById('queryInterval').value = config.queryInterval;

                // 恢复查询配置
                document.getElementById('autoTraverseOutlets').checked = config.autoTraverseOutlets !== false;
                document.getElementById('autoTraverseAreas').checked = config.autoTraverseAreas !== false;
                document.getElementById('autoTraverseCompanies').checked = config.autoTraverseCompanies !== false;
                document.getElementById('autoTraverseMonitorItems').checked = config.autoTraverseMonitorItems !== false;

                // 恢复检测规则
                if (config.thresholdValue) document.getElementById('thresholdValue').value = config.thresholdValue;
                document.getElementById('detectNullValues').checked = config.detectNullValues !== false;
                document.getElementById('detectExceedance').checked = config.detectExceedance !== false;

                // 恢复保存设置
                if (config.savePath) document.getElementById('savePath').value = config.savePath;
                document.getElementById('autoScreenshot').checked = config.autoScreenshot !== false;
            }
        } catch (error) {
            console.error('Failed to load config:', error);
        }
    }

    async saveConfig() {
        const config = {
            timeMode: document.querySelector('input[name="timeMode"]:checked').value,
            startTime: document.getElementById('startTime').value,
            endTime: document.getElementById('endTime').value,
            queryInterval: parseInt(document.getElementById('queryInterval').value),
            autoTraverseOutlets: document.getElementById('autoTraverseOutlets').checked,
            autoTraverseAreas: document.getElementById('autoTraverseAreas').checked,
            autoTraverseCompanies: document.getElementById('autoTraverseCompanies').checked,
            autoTraverseMonitorItems: document.getElementById('autoTraverseMonitorItems').checked,
            thresholdValue: parseFloat(document.getElementById('thresholdValue').value) || null,
            detectNullValues: document.getElementById('detectNullValues').checked,
            detectExceedance: document.getElementById('detectExceedance').checked,
            savePath: document.getElementById('savePath').value,
            autoScreenshot: document.getElementById('autoScreenshot').checked
        };
        
        try {
            await chrome.storage.local.set({ collectorConfig: config });
        } catch (error) {
            console.error('Failed to save config:', error);
        }
    }

    async updatePageInfo() {
        try {
            const response = await chrome.tabs.sendMessage(this.currentTab.id, {
                action: 'getPageInfo'
            });

            if (response) {
                if (response.isEnvironmentSystem) {
                    this.updateStatus('已识别环境监测系统', 'ready');

                    // 更新检测到的时间控件信息
                    this.updateDetectedTimeControls(response);
                } else {
                    this.updateStatus('未识别为环境监测系统', 'warning');
                }
            }
        } catch (error) {
            console.error('Failed to get page info:', error);
            this.updateStatus('无法连接到页面', 'error');
        }
    }

    updateDetectedTimeControls(pageInfo) {
        const timeControlsElement = document.getElementById('detectedTimeControls');
        const hourOptionsElement = document.getElementById('detectedHourOptions');

        if (pageInfo.hasTimeInputs) {
            timeControlsElement.textContent = '✓ 已检测到时间输入框';
            timeControlsElement.style.color = '#4CAF50';
        } else {
            timeControlsElement.textContent = '✗ 未检测到时间输入框';
            timeControlsElement.style.color = '#f44336';
        }

        // 这里需要获取更详细的页面信息
        this.getDetailedPageInfo();
    }

    async getDetailedPageInfo() {
        try {
            const response = await chrome.tabs.sendMessage(this.currentTab.id, {
                action: 'getDetailedTimeInfo'
            });

            if (response) {
                const hourOptionsElement = document.getElementById('detectedHourOptions');

                if (response.hourOptions && response.hourOptions.length > 0) {
                    hourOptionsElement.textContent = `✓ ${response.hourOptions.length}个选项`;
                    hourOptionsElement.style.color = '#4CAF50';
                } else {
                    hourOptionsElement.textContent = '✗ 未检测到小时选项';
                    hourOptionsElement.style.color = '#f44336';
                }
            }
        } catch (error) {
            console.error('Failed to get detailed page info:', error);
        }
    }

    onTimeModeChange() {
        const usePageTime = document.getElementById('usePageTime').checked;
        const customTimeInputs = document.getElementById('customTimeInputs');
        const pageTimeInfo = document.getElementById('pageTimeInfo');

        if (usePageTime) {
            customTimeInputs.style.display = 'none';
            pageTimeInfo.style.display = 'block';
        } else {
            customTimeInputs.style.display = 'block';
            pageTimeInfo.style.display = 'none';
        }

        this.saveConfig();
    }

    async startCollection() {
        if (this.isCollecting) return;
        
        // 验证配置
        const validation = this.validateConfig();
        if (!validation.valid) {
            alert(validation.message);
            return;
        }
        
        // 获取配置
        const config = await this.getConfig();
        
        try {
            // 发送开始采集消息
            await chrome.tabs.sendMessage(this.currentTab.id, {
                action: 'startCollection',
                config: config
            });
            
            this.isCollecting = true;
            this.updateCollectionState(true);
            this.updateStatus('正在采集数据...', 'active');
            
            // 显示进度区域
            document.getElementById('progressSection').style.display = 'block';
            
        } catch (error) {
            console.error('Failed to start collection:', error);
            alert('启动采集失败: ' + error.message);
        }
    }

    async stopCollection() {
        if (!this.isCollecting) return;
        
        try {
            await chrome.tabs.sendMessage(this.currentTab.id, {
                action: 'stopCollection'
            });
            
            this.isCollecting = false;
            this.updateCollectionState(false);
            this.updateStatus('采集已停止', 'stopped');
            
        } catch (error) {
            console.error('Failed to stop collection:', error);
        }
    }

    async pauseCollection() {
        if (!this.isCollecting) return;
        
        try {
            await chrome.tabs.sendMessage(this.currentTab.id, {
                action: 'pauseCollection'
            });
            
        } catch (error) {
            console.error('Failed to pause collection:', error);
        }
    }

    validateConfig() {
        const startTime = document.getElementById('startTime').value;
        const endTime = document.getElementById('endTime').value;
        
        if (!startTime || !endTime) {
            return { valid: false, message: '请设置开始时间和结束时间' };
        }
        
        if (new Date(startTime) >= new Date(endTime)) {
            return { valid: false, message: '开始时间必须早于结束时间' };
        }
        
        const savePath = document.getElementById('savePath').value;
        const autoScreenshot = document.getElementById('autoScreenshot').checked;
        
        if (autoScreenshot && !savePath) {
            return { valid: false, message: '启用自动截图时必须选择保存路径' };
        }
        
        return { valid: true };
    }

    async getConfig() {
        return {
            timeMode: document.querySelector('input[name="timeMode"]:checked').value,
            startTime: document.getElementById('startTime').value,
            endTime: document.getElementById('endTime').value,
            queryInterval: parseInt(document.getElementById('queryInterval').value),
            autoTraverseOutlets: document.getElementById('autoTraverseOutlets').checked,
            autoTraverseAreas: document.getElementById('autoTraverseAreas').checked,
            autoTraverseCompanies: document.getElementById('autoTraverseCompanies').checked,
            autoTraverseMonitorItems: document.getElementById('autoTraverseMonitorItems').checked,
            thresholdValue: parseFloat(document.getElementById('thresholdValue').value) || null,
            detectNullValues: document.getElementById('detectNullValues').checked,
            detectExceedance: document.getElementById('detectExceedance').checked,
            savePath: document.getElementById('savePath').value,
            autoScreenshot: document.getElementById('autoScreenshot').checked
        };
    }

    updateCollectionState(isCollecting) {
        document.getElementById('startCollection').disabled = isCollecting;
        document.getElementById('stopCollection').disabled = !isCollecting;
        document.getElementById('pauseCollection').disabled = !isCollecting;
    }

    updateStatus(text, type) {
        const statusText = document.getElementById('statusText');
        const statusIndicator = document.getElementById('statusIndicator');
        
        statusText.textContent = text;
        
        // 移除所有状态类
        statusIndicator.className = 'status-indicator';
        
        // 添加对应状态类
        switch (type) {
            case 'active':
                statusIndicator.classList.add('active');
                break;
            case 'error':
                statusIndicator.classList.add('error');
                break;
            case 'ready':
                statusIndicator.classList.add('active');
                break;
            default:
                // 默认状态
                break;
        }
    }

    startProgressMonitoring() {
        this.progressInterval = setInterval(async () => {
            if (this.isCollecting) {
                try {
                    const progress = await chrome.tabs.sendMessage(this.currentTab.id, {
                        action: 'getProgress'
                    });
                    
                    if (progress) {
                        this.updateProgress(progress);
                    }
                } catch (error) {
                    // 忽略错误，可能是页面刷新或关闭
                }
            }
        }, 1000);
    }

    updateProgress(progress) {
        document.getElementById('progressFill').style.width = `${progress.progress}%`;
        document.getElementById('progressText').textContent = `${progress.progress}%`;
        document.getElementById('currentTask').textContent = progress.currentTask || '准备中...';
        document.getElementById('processedCount').textContent = progress.processedCount || 0;
        document.getElementById('anomalyCount').textContent = progress.anomalyCount || 0;
        document.getElementById('screenshotCount').textContent = progress.screenshotCount || 0;
        
        if (!progress.isCollecting && this.isCollecting) {
            // 采集完成
            this.isCollecting = false;
            this.updateCollectionState(false);
            this.updateStatus('采集完成', 'ready');
        }
    }

    formatDateTime(date) {
        return date.toISOString().slice(0, 16);
    }

    async selectSaveFolder() {
        // 注意：由于浏览器安全限制，无法直接选择文件夹
        // 这里提供一个输入框让用户手动输入路径
        const path = prompt('请输入保存截图的文件夹路径:', 'C:\\Screenshots\\');
        if (path) {
            document.getElementById('savePath').value = path;
            await this.saveConfig();
        }
    }

    async exportReport() {
        // 导出采集报告
        try {
            const result = await chrome.storage.local.get('lastReport');
            if (result.lastReport) {
                const blob = new Blob([JSON.stringify(result.lastReport, null, 2)], {
                    type: 'application/json'
                });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `环境监测数据报告_${new Date().toISOString().slice(0, 10)}.json`;
                a.click();
                URL.revokeObjectURL(url);
            } else {
                alert('暂无报告数据');
            }
        } catch (error) {
            console.error('Export failed:', error);
            alert('导出失败: ' + error.message);
        }
    }

    viewLogs() {
        chrome.tabs.create({ url: chrome.runtime.getURL('logs.html') });
    }

    openOptions() {
        chrome.runtime.openOptionsPage();
    }
}

// 初始化popup控制器
document.addEventListener('DOMContentLoaded', () => {
    new PopupController();
});
