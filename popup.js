// 环境监测数据采集器 - Popup Script
class PopupController {
    constructor() {
        this.isCollecting = false;
        this.currentTab = null;
        this.progressInterval = null;
        
        this.init();
    }

    async init() {
        // 获取当前标签页
        const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
        this.currentTab = tabs[0];
        
        // 初始化UI
        this.initializeUI();
        
        // 绑定事件
        this.bindEvents();
        
        // 加载保存的配置
        await this.loadConfig();
        
        // 获取页面信息
        await this.updatePageInfo();
        
        // 开始监听进度更新
        this.startProgressMonitoring();
    }

    initializeUI() {
        // 设置状态
        this.updateStatus('待配置', 'waiting');
    }

    bindEvents() {
        // 查询配置事件（移除了时间配置相关事件）
        
        // 查询配置事件
        document.getElementById('autoTraverseOutlets').addEventListener('change', () => this.saveConfig());
        document.getElementById('autoTraverseAreas').addEventListener('change', () => this.saveConfig());
        document.getElementById('autoTraverseCompanies').addEventListener('change', () => this.saveConfig());
        document.getElementById('autoTraverseMonitorItems').addEventListener('change', () => this.saveConfig());
        
        // 检测规则事件
        document.getElementById('thresholdValue').addEventListener('input', () => this.saveConfig());
        document.getElementById('detectNullValues').addEventListener('change', () => this.saveConfig());
        document.getElementById('detectExceedance').addEventListener('change', () => this.saveConfig());
        
        // 保存设置事件
        document.getElementById('selectFolder').addEventListener('click', () => this.selectSaveFolder());
        document.getElementById('autoScreenshot').addEventListener('change', () => this.saveConfig());
        
        // 控制按钮事件
        document.getElementById('startCollection').addEventListener('click', () => this.startCollection());
        document.getElementById('stopCollection').addEventListener('click', () => this.stopCollection());
        document.getElementById('pauseCollection').addEventListener('click', () => this.pauseCollection());
        
        // 操作按钮事件
        document.getElementById('exportReport').addEventListener('click', () => this.exportReport());
        document.getElementById('viewLogs').addEventListener('click', () => this.viewLogs());
        document.getElementById('openOptions').addEventListener('click', () => this.openOptions());
    }

    async loadConfig() {
        try {
            const result = await chrome.storage.local.get('collectorConfig');
            if (result.collectorConfig) {
                const config = result.collectorConfig;

                // 恢复查询配置
                document.getElementById('autoTraverseOutlets').checked = config.autoTraverseOutlets !== false;
                document.getElementById('autoTraverseAreas').checked = config.autoTraverseAreas !== false;
                document.getElementById('autoTraverseCompanies').checked = config.autoTraverseCompanies !== false;
                document.getElementById('autoTraverseMonitorItems').checked = config.autoTraverseMonitorItems !== false;

                // 恢复检测规则
                if (config.thresholdValue) document.getElementById('thresholdValue').value = config.thresholdValue;
                document.getElementById('detectNullValues').checked = config.detectNullValues !== false;
                document.getElementById('detectExceedance').checked = config.detectExceedance !== false;

                // 恢复保存设置
                if (config.savePath) document.getElementById('savePath').value = config.savePath;
                document.getElementById('autoScreenshot').checked = config.autoScreenshot !== false;
            }
        } catch (error) {
            console.error('Failed to load config:', error);
        }
    }

    async saveConfig() {
        const config = {
            autoTraverseOutlets: document.getElementById('autoTraverseOutlets').checked,
            autoTraverseAreas: document.getElementById('autoTraverseAreas').checked,
            autoTraverseCompanies: document.getElementById('autoTraverseCompanies').checked,
            autoTraverseMonitorItems: document.getElementById('autoTraverseMonitorItems').checked,
            thresholdValue: parseFloat(document.getElementById('thresholdValue').value) || null,
            detectNullValues: document.getElementById('detectNullValues').checked,
            detectExceedance: document.getElementById('detectExceedance').checked,
            savePath: document.getElementById('savePath').value,
            autoScreenshot: document.getElementById('autoScreenshot').checked
        };
        
        try {
            await chrome.storage.local.set({ collectorConfig: config });
        } catch (error) {
            console.error('Failed to save config:', error);
        }
    }

    async ensureContentScriptInjected() {
        try {
            // 尝试ping content script
            await chrome.tabs.sendMessage(this.currentTab.id, { action: 'ping' });
        } catch (error) {
            console.log('Content script未注入，正在注入...');

            // 注入content script
            await chrome.scripting.executeScript({
                target: { tabId: this.currentTab.id },
                files: ['content.js']
            });

            // 等待一下让content script初始化
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }

    async updatePageInfo() {
        try {
            // 确保content script已注入
            await this.ensureContentScriptInjected();

            const response = await chrome.tabs.sendMessage(this.currentTab.id, {
                action: 'getPageInfo'
            });

            console.log('页面信息响应:', response);

            if (response && response.success && response.data) {
                const pageInfo = response.data;
                if (pageInfo.isEnvironmentSystem) {
                    this.updateStatus('已识别环境监测系统', 'ready');
                } else {
                    this.updateStatus('未识别为环境监测系统', 'warning');
                }
            } else {
                this.updateStatus('获取页面信息失败', 'error');
            }
        } catch (error) {
            console.error('Failed to get page info:', error);
            this.updateStatus('无法连接到页面，请刷新页面重试', 'error');
        }
    }



    async startCollection() {
        if (this.isCollecting) return;

        // 验证配置
        const validation = this.validateConfig();
        if (!validation.valid) {
            alert(validation.message);
            return;
        }

        // 获取配置
        const config = await this.getConfig();

        try {
            // 首先检查content script是否已注入
            await this.ensureContentScriptInjected();

            // 发送开始采集消息
            const response = await chrome.tabs.sendMessage(this.currentTab.id, {
                action: 'startCollection',
                config: config
            });

            console.log('采集启动响应:', response);

            if (response && response.success) {
                this.isCollecting = true;
                this.updateCollectionState(true);
                this.updateStatus('正在采集数据...', 'active');

                // 显示进度区域
                document.getElementById('progressSection').style.display = 'block';
            } else {
                const errorMsg = response && response.error ? response.error : '未知错误';
                throw new Error('启动失败: ' + errorMsg);
            }

        } catch (error) {
            console.error('Failed to start collection:', error);

            if (error.message.includes('Could not establish connection')) {
                alert('连接失败：请刷新页面后重试，或检查页面是否完全加载');
            } else {
                alert('启动采集失败: ' + error.message);
            }
        }
    }

    async stopCollection() {
        if (!this.isCollecting) return;

        try {
            await chrome.tabs.sendMessage(this.currentTab.id, {
                action: 'stopCollection'
            });

            this.isCollecting = false;
            this.updateCollectionState(false);
            this.updateStatus('采集已停止', 'stopped');

        } catch (error) {
            console.error('Failed to stop collection:', error);
            // 即使通信失败，也要更新本地状态
            this.isCollecting = false;
            this.updateCollectionState(false);
            this.updateStatus('采集已停止', 'stopped');
        }
    }

    async pauseCollection() {
        if (!this.isCollecting) return;

        try {
            await chrome.tabs.sendMessage(this.currentTab.id, {
                action: 'pauseCollection'
            });

        } catch (error) {
            console.error('Failed to pause collection:', error);
        }
    }

    validateConfig() {
        const savePath = document.getElementById('savePath').value;
        const autoScreenshot = document.getElementById('autoScreenshot').checked;

        if (autoScreenshot && !savePath) {
            return { valid: false, message: '启用自动截图时必须选择保存路径' };
        }

        return { valid: true };
    }

    async getConfig() {
        return {
            autoTraverseOutlets: document.getElementById('autoTraverseOutlets').checked,
            autoTraverseAreas: document.getElementById('autoTraverseAreas').checked,
            autoTraverseCompanies: document.getElementById('autoTraverseCompanies').checked,
            autoTraverseMonitorItems: document.getElementById('autoTraverseMonitorItems').checked,
            thresholdValue: parseFloat(document.getElementById('thresholdValue').value) || null,
            detectNullValues: document.getElementById('detectNullValues').checked,
            detectExceedance: document.getElementById('detectExceedance').checked,
            savePath: document.getElementById('savePath').value,
            autoScreenshot: document.getElementById('autoScreenshot').checked
        };
    }

    updateCollectionState(isCollecting) {
        document.getElementById('startCollection').disabled = isCollecting;
        document.getElementById('stopCollection').disabled = !isCollecting;
        document.getElementById('pauseCollection').disabled = !isCollecting;
    }

    updateStatus(text, type) {
        const statusText = document.getElementById('statusText');
        const statusIndicator = document.getElementById('statusIndicator');
        
        statusText.textContent = text;
        
        // 移除所有状态类
        statusIndicator.className = 'status-indicator';
        
        // 添加对应状态类
        switch (type) {
            case 'active':
                statusIndicator.classList.add('active');
                break;
            case 'error':
                statusIndicator.classList.add('error');
                break;
            case 'ready':
                statusIndicator.classList.add('active');
                break;
            default:
                // 默认状态
                break;
        }
    }

    startProgressMonitoring() {
        this.progressInterval = setInterval(async () => {
            if (this.isCollecting) {
                try {
                    const response = await chrome.tabs.sendMessage(this.currentTab.id, {
                        action: 'getProgress'
                    });

                    if (response && response.success && response.data) {
                        this.updateProgress(response.data);
                    }
                } catch (error) {
                    // 如果连接失败，可能是页面刷新或content script崩溃
                    console.log('进度监控连接失败，尝试重新连接...');

                    // 尝试重新建立连接
                    try {
                        await this.ensureContentScriptInjected();
                    } catch (reconnectError) {
                        console.error('重新连接失败:', reconnectError);
                        // 停止采集
                        this.isCollecting = false;
                        this.updateCollectionState(false);
                        this.updateStatus('连接中断，采集已停止', 'error');
                    }
                }
            }
        }, 1000);
    }

    updateProgress(progress) {
        document.getElementById('progressFill').style.width = `${progress.progress}%`;
        document.getElementById('progressText').textContent = `${progress.progress}%`;
        document.getElementById('currentTask').textContent = progress.currentTask || '准备中...';
        document.getElementById('processedCount').textContent = progress.processedCount || 0;
        document.getElementById('anomalyCount').textContent = progress.anomalyCount || 0;
        document.getElementById('screenshotCount').textContent = progress.screenshotCount || 0;
        
        if (!progress.isCollecting && this.isCollecting) {
            // 采集完成
            this.isCollecting = false;
            this.updateCollectionState(false);
            this.updateStatus('采集完成', 'ready');
        }
    }

    formatDateTime(date) {
        return date.toISOString().slice(0, 16);
    }

    async selectSaveFolder() {
        // 注意：由于浏览器安全限制，无法直接选择文件夹
        // 这里提供一个输入框让用户手动输入路径
        const path = prompt('请输入保存截图的文件夹路径:', 'C:\\Screenshots\\');
        if (path) {
            document.getElementById('savePath').value = path;
            await this.saveConfig();
        }
    }

    async exportReport() {
        // 导出采集报告
        try {
            const result = await chrome.storage.local.get('lastReport');
            if (result.lastReport) {
                const blob = new Blob([JSON.stringify(result.lastReport, null, 2)], {
                    type: 'application/json'
                });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `环境监测数据报告_${new Date().toISOString().slice(0, 10)}.json`;
                a.click();
                URL.revokeObjectURL(url);
            } else {
                alert('暂无报告数据');
            }
        } catch (error) {
            console.error('Export failed:', error);
            alert('导出失败: ' + error.message);
        }
    }

    viewLogs() {
        chrome.tabs.create({ url: chrome.runtime.getURL('logs.html') });
    }

    openOptions() {
        chrome.runtime.openOptionsPage();
    }
}

// 初始化popup控制器
document.addEventListener('DOMContentLoaded', () => {
    new PopupController();
});
