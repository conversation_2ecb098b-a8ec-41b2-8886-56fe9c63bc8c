# 环境监测数据自动采集器 - 功能更新说明

## 重要更新：支持页面中的时间控件操作

根据您的反馈，我已经重新设计了插件的时间配置功能，现在插件能够：

### ✅ 新增功能

#### 1. 智能识别页面时间控件
- **自动检测小时选择框**：识别页面中的小时下拉选择
- **自动检测开始时间输入框**：识别页面中的开始时间控件
- **自动检测结束时间输入框**：识别页面中的结束时间控件
- **支持多种输入框类型**：text、datetime-local、date、time等

#### 2. 两种时间配置模式
**模式一：使用页面中的时间控件（推荐）**
- 插件自动操作页面中现有的时间控件
- 自动遍历页面中的所有小时选项
- 自动设置页面中的开始时间和结束时间
- 完全模拟用户在页面中的操作

**模式二：自定义时间范围**
- 用户在插件中手动设置时间范围
- 适用于页面时间控件识别失败的情况
- 保持原有的时间配置功能

#### 3. 智能时间格式适配
- 自动识别输入框的时间格式要求
- 支持多种时间格式：
  - `yyyy-mm-dd hh:mm`
  - `yyyy-mm-dd`
  - `hh:mm`
  - 本地化时间格式

### 🔄 工作流程更新

#### 原来的流程：
1. 用户在插件中设置时间范围
2. 插件生成时间序列
3. 遍历查询各种组合

#### 现在的流程：
1. **页面识别**：自动检测页面中的时间控件
2. **模式选择**：用户选择使用页面控件还是自定义时间
3. **智能遍历**：
   - 如果选择页面控件：遍历页面中的所有小时选项
   - 如果选择自定义：按设定的时间间隔遍历
4. **自动操作**：直接操作页面中的控件进行查询

### 📋 使用说明

#### 使用页面时间控件模式：
1. 打开环境监测系统页面
2. 点击插件图标，选择"使用页面中的时间控件"
3. 插件会显示检测到的时间控件信息：
   - ✓ 已检测到时间输入框
   - ✓ X个小时选项
4. 配置其他参数（排口类型、区域等遍历选项）
5. 点击"开始采集"

#### 插件会自动：
- 遍历页面中的每个小时选项
- 对每个小时选项，遍历所有排口类型
- 对每个排口类型，遍历所有区域
- 对每个区域，遍历所有企业
- 对每个企业，遍历所有监测项目
- 检测异常数据并自动截图

### 🎯 适用场景

这个更新特别适合您描述的场景：
- 页面上方有小时选择下拉框
- 页面有开始时间和结束时间输入框
- 需要遍历所有时间选项和其他参数组合
- 需要检测异常数据并截图保存

### 🔧 技术实现

#### 时间控件识别算法：
```javascript
// 通过多种方式识别时间输入框
const timeKeywords = ['时间', '日期', 'time', 'date', '开始', '结束', 'start', 'end'];
const isTimeInput = timeKeywords.some(keyword => 
    (label && label.includes(keyword)) ||
    placeholder.includes(keyword) ||
    id.toLowerCase().includes(keyword.toLowerCase()) ||
    name.toLowerCase().includes(keyword.toLowerCase())
);
```

#### 智能时间格式化：
```javascript
// 根据输入框类型自动格式化时间
if (type === 'datetime-local') {
    return date.toISOString().slice(0, 16);
} else if (type === 'date') {
    return date.toISOString().slice(0, 10);
} else {
    // 根据placeholder判断格式
}
```

### 📊 界面更新

#### 新增的界面元素：
- **时间模式选择器**：单选按钮选择时间配置模式
- **页面时间控件信息**：显示检测到的时间控件状态
- **动态显示/隐藏**：根据选择的模式显示相应的配置选项

### 🚀 优势

1. **更贴近实际使用**：直接操作页面中的控件，就像用户手动操作一样
2. **更高的兼容性**：适应不同环境监测系统的界面设计
3. **更智能的识别**：自动检测和适配页面中的时间控件
4. **更灵活的配置**：支持两种模式，适应不同的使用场景

### 📝 注意事项

1. **首次使用**：建议先选择"使用页面中的时间控件"模式
2. **控件检测**：如果页面时间控件检测失败，可切换到自定义模式
3. **时间格式**：插件会自动适配页面要求的时间格式
4. **操作延迟**：插件会在操作间添加适当延迟，确保页面响应

### 🔄 升级方法

如果您已经安装了旧版本：
1. 在Chrome扩展程序页面删除旧版本
2. 重新加载新版本的插件文件夹
3. 重新配置您的采集参数

---

这个更新完全满足了您描述的需求：操作页面中现有的小时选择、开始时间、结束时间控件，并自动遍历所有组合进行数据采集和异常检测。
