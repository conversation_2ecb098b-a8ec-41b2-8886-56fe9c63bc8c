# 页面初始化调试工具

## 问题分析

根据HTML源码和调试结果，页面中的下拉框元素是动态创建的，需要等待JavaScript初始化完成。

## 手动触发页面初始化

请在控制台中依次执行以下代码：

### 第一步：检查页面加载状态

```javascript
console.log('=== 页面加载状态检查 ===');
console.log('文档状态:', document.readyState);
console.log('jQuery:', typeof window.$);
console.log('HistoryReport:', typeof window.HistoryReport);
console.log('Bootstrap Select:', !!(window.$ && window.$.fn && window.$.fn.selectpicker));

// 检查关键脚本是否加载
console.log('关键脚本检查:');
console.log('- HistoryData.js:', !!window.HistoryReport);
console.log('- bootstrap-select.js:', !!(window.$ && window.$.fn && window.$.fn.selectpicker));
console.log('- jquery.easyui.js:', !!(window.$ && window.$.fn && window.$.fn.datagrid));
```

### 第二步：手动触发HistoryReport初始化

```javascript
console.log('=== 手动触发初始化 ===');

// 检查HistoryReport对象
if (window.HistoryReport) {
    console.log('HistoryReport对象存在，尝试初始化...');
    
    // 调用初始化函数
    if (typeof window.HistoryReport.Init === 'function') {
        console.log('调用 HistoryReport.Init()');
        window.HistoryReport.Init();
        
        // 等待3秒后检查结果
        setTimeout(() => {
            console.log('初始化后检查:');
            ['sel_SubType', 'sel_City', 'sel_ent', 'select_sub', 'sel_Item'].forEach(id => {
                const element = document.getElementById(id);
                console.log(`${id}:`, {
                    exists: !!element,
                    options: element ? element.options.length : 0,
                    value: element ? element.value : 'N/A'
                });
            });
        }, 3000);
    } else {
        console.log('HistoryReport.Init 方法不存在');
        console.log('HistoryReport 可用方法:', Object.keys(window.HistoryReport));
    }
} else {
    console.log('HistoryReport对象不存在，检查脚本加载...');
    
    // 检查脚本标签
    const scripts = document.querySelectorAll('script[src*="HistoryData"]');
    console.log('HistoryData脚本标签:', scripts.length);
    scripts.forEach((script, index) => {
        console.log(`脚本 ${index + 1}:`, script.src);
    });
}
```

### 第三步：手动初始化Bootstrap Select

```javascript
console.log('=== 手动初始化 Bootstrap Select ===');

if (window.$ && window.$.fn && window.$.fn.selectpicker) {
    console.log('Bootstrap Select 可用，开始初始化...');
    
    // 查找所有selectpicker元素
    const selectpickers = document.querySelectorAll('.selectpicker');
    console.log(`找到 ${selectpickers.length} 个 .selectpicker 元素`);
    
    // 手动初始化
    $('.selectpicker').selectpicker();
    
    // 检查初始化结果
    setTimeout(() => {
        console.log('Bootstrap Select 初始化后检查:');
        selectpickers.forEach((select, index) => {
            const $select = $(select);
            console.log(`Selectpicker ${index + 1} (${select.id}):`, {
                initialized: !!$select.data('selectpicker'),
                options: select.options.length,
                container: !!select.parentElement.querySelector('.bootstrap-select')
            });
        });
    }, 1000);
} else {
    console.log('Bootstrap Select 不可用');
    console.log('jQuery:', typeof window.$);
    console.log('selectpicker:', !!(window.$ && window.$.fn && window.$.fn.selectpicker));
}
```

### 第四步：检查页面完整性

```javascript
console.log('=== 页面完整性检查 ===');

// 检查所有预期元素
const expectedElements = [
    'sel_SubType', 'sel_City', 'sel_ent', 'select_sub', 'sel_Item',
    'dataBasic', 'startHours', 'endHours'
];

console.log('预期元素检查:');
expectedElements.forEach(id => {
    const element = document.getElementById(id);
    console.log(`${id}:`, {
        exists: !!element,
        visible: element ? element.offsetParent !== null : false,
        type: element ? element.tagName : 'N/A'
    });
});

// 检查查询按钮
const queryButtons = document.querySelectorAll('button[onclick*="doSearch"]');
console.log(`查询按钮: ${queryButtons.length} 个`);
queryButtons.forEach((btn, index) => {
    console.log(`按钮 ${index + 1}:`, {
        text: btn.textContent.trim(),
        onclick: btn.getAttribute('onclick')
    });
});
```

### 第五步：测试插件重新识别

```javascript
console.log('=== 测试插件重新识别 ===');

// 检查插件对象
if (window.collector) {
    console.log('插件对象存在，重新分析页面...');
    
    // 重新分析页面结构
    window.collector.analyzePageStructure();
    
    console.log('重新分析结果:');
    console.log('- 下拉框数量:', window.collector.pageElements?.dropdowns?.length || 0);
    console.log('- 查询按钮:', !!window.collector.pageElements?.queryButton);
    console.log('- 数据表格:', !!window.collector.pageElements?.dataTable);
    
    if (window.collector.pageElements?.dropdowns) {
        window.collector.pageElements.dropdowns.forEach((dropdown, index) => {
            console.log(`下拉框 ${index + 1}:`, {
                id: dropdown.id,
                type: dropdown.type,
                label: dropdown.label,
                options: dropdown.options?.length || 0
            });
        });
    }
} else {
    console.log('插件对象不存在');
}
```

## 预期结果

执行完这些步骤后，您应该看到：

### 成功的情况：
```
HistoryReport对象存在，尝试初始化...
调用 HistoryReport.Init()
初始化后检查:
sel_SubType: {exists: true, options: 8, value: ""}
sel_City: {exists: true, options: 12, value: ""}
...

Bootstrap Select 可用，开始初始化...
找到 5 个 .selectpicker 元素
Bootstrap Select 初始化后检查:
Selectpicker 1 (sel_SubType): {initialized: true, options: 8, container: true}
...

插件对象存在，重新分析页面...
重新分析结果:
- 下拉框数量: 5
- 查询按钮: true
- 数据表格: true
```

### 如果仍有问题：

1. **HistoryReport不存在**：脚本可能加载失败，检查网络请求
2. **Bootstrap Select不可用**：检查bootstrap-select.js是否正确加载
3. **元素仍不存在**：可能需要特定的触发条件或权限

## 解决方案

根据测试结果：

### 方案A：等待更长时间
如果脚本正在加载中：
```javascript
// 等待10秒后重试
setTimeout(() => {
    console.log('延迟重试...');
    // 重新执行初始化步骤
}, 10000);
```

### 方案B：手动触发特定事件
如果需要特定触发条件：
```javascript
// 尝试触发页面事件
document.dispatchEvent(new Event('DOMContentLoaded'));
window.dispatchEvent(new Event('load'));
```

### 方案C：检查权限或登录状态
如果页面需要特定权限：
```javascript
// 检查是否需要登录或权限
console.log('当前URL:', window.location.href);
console.log('页面标题:', document.title);
console.log('是否有错误信息:', document.body.textContent.includes('错误') || document.body.textContent.includes('登录'));
```

请执行这些调试步骤并告诉我结果，这样我就能进一步优化插件的初始化逻辑。
