// 环境监测数据自动采集器 - Content Script
class EnvironmentDataCollector {
    constructor() {
        this.isCollecting = false;
        this.isPaused = false;
        this.config = {};
        this.collectedData = [];
        this.anomalies = [];
        this.currentTask = '';
        this.progress = 0;
        this.totalTasks = 0;
        this.processedCount = 0;
        this.isEnvironmentSystem = false;
        this.pageElements = {
            dropdowns: [],
            queryButton: null,
            dataTable: null,
            pagination: null
        };

        this.init();
    }

    init() {
        // 监听来自popup的消息
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            // 对于异步操作，需要返回true来保持消息通道开放
            this.handleMessage(request, sender, sendResponse);
            return true; // 保持消息通道开放
        });

        // 检测页面是否为环境监测系统
        this.detectEnvironmentSystem();

        console.log('环境监测数据采集器已初始化');
    }

    detectEnvironmentSystem() {
        // 检测页面特征，判断是否为环境监测系统
        const indicators = [
            '排口类型', '所属区域', '企业名称', '排口名称', '监测项目',
            '实时数据', '历史数据', '环境监测', '污染源'
        ];

        const pageText = document.body.innerText;
        const matchCount = indicators.filter(indicator =>
            pageText.includes(indicator)
        ).length;

        if (matchCount >= 3) {
            this.isEnvironmentSystem = true;
            this.analyzePageStructure();
        } else {
            // 即使不是环境监测系统，也要初始化页面元素
            this.analyzePageStructure();
        }
    }

    analyzePageStructure() {
        try {
            // 分析页面结构，识别关键元素
            this.pageElements = {
                dropdowns: this.findDropdowns() || [],
                queryButton: this.findQueryButton() || null,
                dataTable: this.findDataTable() || null,
                pagination: this.findPagination() || null
            };

            console.log('页面元素分析完成:', this.pageElements);
        } catch (error) {
            console.error('页面结构分析失败:', error);
            // 确保pageElements有默认值
            this.pageElements = {
                dropdowns: [],
                queryButton: null,
                dataTable: null,
                pagination: null
            };
        }
    }



    findDropdowns() {
        try {
            const dropdowns = [];

            // 根据HTML源码，这个页面使用Bootstrap Select
            // 主要的下拉框ID：sel_SubType, sel_City, sel_ent, select_sub, sel_Item

            // 1. 查找特定ID的Bootstrap Select下拉框
            const bootstrapSelectIds = [
                'sel_SubType',  // 排口类型
                'sel_City',     // 所属地区
                'sel_ent',      // 企业名称
                'select_sub',   // 排口名称
                'sel_Item'      // 监测项目
            ];

            console.log('查找Bootstrap Select下拉框...');

            bootstrapSelectIds.forEach(id => {
                const select = document.getElementById(id);
                if (select) {
                    const type = this.getDropdownTypeById(id);
                    const label = this.getBootstrapSelectLabel(select);

                    // 获取Bootstrap Select的选项
                    const options = this.getBootstrapSelectOptions(select);

                    dropdowns.push({
                        element: select,
                        label: label,
                        type: type,
                        id: id,
                        name: select.name || '',
                        options: options,
                        elementType: 'bootstrap-select'
                    });

                    console.log(`Bootstrap Select: ${id} (${type}) - ${label}, 选项数: ${options.length}`);
                }
            });

            // 2. 查找其他标准select元素
            const allSelects = document.querySelectorAll('select');
            console.log(`找到 ${allSelects.length} 个标准select元素`);

            allSelects.forEach((select, index) => {
                // 跳过已经处理过的Bootstrap Select
                if (!bootstrapSelectIds.includes(select.id)) {
                    this.processStandardSelect(select, dropdowns, index);
                }
            });

            // 3. 查找Bootstrap Select的按钮触发器
            const bootstrapButtons = document.querySelectorAll('.bootstrap-select .dropdown-toggle');
            console.log(`找到 ${bootstrapButtons.length} 个Bootstrap Select按钮`);

            bootstrapButtons.forEach((button, index) => {
                const parentSelect = button.closest('.bootstrap-select');
                if (parentSelect) {
                    const originalSelect = parentSelect.querySelector('select');
                    if (originalSelect && !dropdowns.find(d => d.element === originalSelect)) {
                        this.processBootstrapSelectButton(button, originalSelect, dropdowns, index);
                    }
                }
            });

            console.log(`总共识别了 ${dropdowns.length} 个下拉框控件`);
            return dropdowns;
        } catch (error) {
            console.error('查找下拉框时出错:', error);
            return [];
        }
    }

    getDropdownTypeById(id) {
        switch (id) {
            case 'sel_SubType': return 'outlet';      // 排口类型
            case 'sel_City': return 'area';           // 所属地区
            case 'sel_ent': return 'company';         // 企业名称
            case 'select_sub': return 'outlet_name';  // 排口名称
            case 'sel_Item': return 'monitor_item';   // 监测项目
            default: return 'unknown';
        }
    }

    getBootstrapSelectLabel(select) {
        // 查找相关的标签文本
        const formGroup = select.closest('.form-group');
        if (formGroup) {
            const labelDiv = formGroup.querySelector('.Searh_text');
            if (labelDiv) {
                return labelDiv.textContent?.trim().replace('：', '');
            }
        }
        return select.id || '未知下拉框';
    }

    getBootstrapSelectOptions(select) {
        const options = [];

        // 获取原始select的选项
        Array.from(select.options).forEach(option => {
            if (option.value && option.value !== '') {
                options.push({
                    value: option.value,
                    text: option.text.trim()
                });
            }
        });

        return options;
    }

    processStandardSelect(select, dropdowns, index) {
        try {
            const label = this.getInputLabel(select);
            const id = select.id || '';
            const name = select.name || '';

            // 判断下拉框类型
            let type = this.determineDropdownType(label, id, name, '');

            // 获取选项
            const options = Array.from(select.options).map(opt => ({
                value: opt.value,
                text: opt.text.trim()
            })).filter(opt => opt.value && opt.text);

            if (type !== 'unknown' || options.length > 0) {
                dropdowns.push({
                    element: select,
                    label: label || `标准下拉框${index + 1}`,
                    type: type,
                    id: id,
                    name: name,
                    options: options,
                    elementType: 'select'
                });

                console.log(`标准下拉框 ${index + 1}: ${label || id || name}, 类型: ${type}, 选项数: ${options.length}`);
            }
        } catch (error) {
            console.error(`处理标准下拉框 ${index} 时出错:`, error);
        }
    }

    processBootstrapSelectButton(button, originalSelect, dropdowns, index) {
        try {
            const label = this.getBootstrapSelectLabel(originalSelect);
            const id = originalSelect.id || '';
            const type = this.getDropdownTypeById(id) || this.determineDropdownType(label, id, '', '');
            const options = this.getBootstrapSelectOptions(originalSelect);

            dropdowns.push({
                element: originalSelect,
                button: button, // 保存按钮引用，用于点击操作
                label: label,
                type: type,
                id: id,
                name: originalSelect.name || '',
                options: options,
                elementType: 'bootstrap-select-button'
            });

            console.log(`Bootstrap Select按钮 ${index + 1}: ${label}, 类型: ${type}, 选项数: ${options.length}`);
        } catch (error) {
            console.error(`处理Bootstrap Select按钮 ${index} 时出错:`, error);
        }
    }

    determineDropdownType(label, id, name, text) {
        const allText = `${label} ${id} ${name} ${text}`.toLowerCase();

        // 根据页面源码的具体ID和标签进行匹配
        if (id === 'sel_SubType' || allText.includes('排口类型')) {
            return 'outlet';
        } else if (id === 'sel_City' || allText.includes('所属地区') || allText.includes('地区')) {
            return 'area';
        } else if (id === 'sel_ent' || allText.includes('企业名称') || allText.includes('企业')) {
            return 'company';
        } else if (id === 'select_sub' || allText.includes('排口名称')) {
            return 'outlet_name';
        } else if (id === 'sel_Item' || allText.includes('监测项目') || allText.includes('项目')) {
            return 'monitor_item';
        }

        // 通用匹配
        if (allText.includes('排口') || allText.includes('出口')) {
            return 'outlet';
        } else if (allText.includes('区域') || allText.includes('地区')) {
            return 'area';
        } else if (allText.includes('企业') || allText.includes('公司')) {
            return 'company';
        } else if (allText.includes('监测') || allText.includes('项目')) {
            return 'monitor_item';
        }

        return 'unknown';
    }

    findQueryButton() {
        try {
            // 根据HTML源码，查询按钮的onclick是HistoryReport.doSearch()
            console.log('查找查询按钮...');

            // 1. 优先查找具有特定onclick事件的按钮
            const buttons = document.querySelectorAll('button');

            for (let button of buttons) {
                const onclick = button.getAttribute('onclick') || '';
                const text = button.textContent?.trim() || '';

                // 根据源码，查询按钮的onclick是HistoryReport.doSearch()
                if (onclick.includes('HistoryReport.doSearch()') ||
                    onclick.includes('doSearch') ||
                    text.includes('查 询') || text.includes('查询')) {
                    console.log('找到查询按钮:', text, 'onclick:', onclick);
                    return button;
                }
            }

            // 2. 备用方案：查找包含"查询"文本的按钮
            const allButtons = document.querySelectorAll('button, input[type="button"], input[type="submit"]');
            console.log(`找到 ${allButtons.length} 个按钮元素`);

            for (let button of allButtons) {
                const text = button.textContent || button.value || '';
                const id = button.id || '';

                if (text.includes('查询') || text.includes('搜索') || text.includes('检索') ||
                    text.includes('查 询') || id.includes('search') || id.includes('query')) {
                    console.log('找到查询按钮(备用):', text || id);
                    return button;
                }
            }

            console.log('未找到查询按钮');
            return null;
        } catch (error) {
            console.error('查找查询按钮时出错:', error);
            return null;
        }
    }

    findDataTable() {
        try {
            console.log('查找数据表格...');

            // 根据HTML源码，数据表格使用EasyUI DataGrid，ID为dataBasic
            const dataBasicTable = document.getElementById('dataBasic');
            if (dataBasicTable) {
                console.log('找到EasyUI DataGrid表格: dataBasic');
                return dataBasicTable;
            }

            // 查找EasyUI相关的表格
            const easyuiTables = document.querySelectorAll('.datagrid, .datagrid-view, [class*="datagrid"]');
            if (easyuiTables.length > 0) {
                console.log(`找到 ${easyuiTables.length} 个EasyUI表格`);
                return easyuiTables[0];
            }

            // 查找divGrid容器中的表格
            const divGrid = document.getElementById('divGrid');
            if (divGrid) {
                const tablesInGrid = divGrid.querySelectorAll('table');
                if (tablesInGrid.length > 0) {
                    console.log(`在divGrid中找到 ${tablesInGrid.length} 个表格`);
                    return tablesInGrid[0];
                }
            }

            // 备用方案：查找标准表格
            const tables = document.querySelectorAll('table');
            console.log(`找到 ${tables.length} 个表格`);

            for (let table of tables) {
                const headers = table.querySelectorAll('th');
                const rows = table.querySelectorAll('tr');
                const id = table.id || '';
                const className = table.className || '';

                // 优先选择有数据特征的表格
                if (headers.length >= 3 || rows.length >= 5 ||
                    id.includes('data') || className.includes('data') ||
                    id.includes('grid') || className.includes('grid')) {
                    console.log(`找到数据表格: ${id || className}, ${headers.length} 列, ${rows.length} 行`);
                    return table;
                }
            }

            // 查找其他可能的数据容器
            const dataContainers = document.querySelectorAll(`
                .data-table, .grid, .datagrid, [class*="table"],
                .page-data, [id*="data"], [id*="grid"]
            `);
            if (dataContainers.length > 0) {
                console.log('找到数据容器:', dataContainers[0].className || dataContainers[0].id);
                return dataContainers[0];
            }

            console.log('未找到数据表格');
            return null;
        } catch (error) {
            console.error('查找数据表格时出错:', error);
            return null;
        }
    }

    findPagination() {
        // 查找分页元素
        const paginationSelectors = [
            '.pagination', '.pager', '.page-nav',
            '[class*="page"]', '[class*="paging"]'
        ];
        
        for (let selector of paginationSelectors) {
            const element = document.querySelector(selector);
            if (element) return element;
        }
        
        return null;
    }

    getInputLabel(input) {
        // 获取输入框的标签文本
        const id = input.id;
        if (id) {
            const label = document.querySelector(`label[for="${id}"]`);
            if (label) return label.textContent.trim();
        }
        
        // 查找前面的文本
        const parent = input.parentElement;
        if (parent) {
            const text = parent.textContent.replace(input.value || '', '').trim();
            if (text.length > 0 && text.length < 20) {
                return text;
            }
        }
        
        return null;
    }

    async handleMessage(request, sender, sendResponse) {
        try {
            console.log('收到消息:', request.action);

            switch (request.action) {
                case 'ping':
                    sendResponse({ success: true, message: 'Content script is ready' });
                    break;

                case 'startCollection':
                    try {
                        await this.startCollection(request.config);
                        sendResponse({ success: true, message: '采集已开始' });
                    } catch (error) {
                        console.error('启动采集失败:', error);
                        sendResponse({ success: false, error: error.message });
                    }
                    break;

                case 'stopCollection':
                    try {
                        this.stopCollection();
                        sendResponse({ success: true, message: '采集已停止' });
                    } catch (error) {
                        console.error('停止采集失败:', error);
                        sendResponse({ success: false, error: error.message });
                    }
                    break;

                case 'pauseCollection':
                    try {
                        this.pauseCollection();
                        sendResponse({ success: true, message: '采集已暂停' });
                    } catch (error) {
                        console.error('暂停采集失败:', error);
                        sendResponse({ success: false, error: error.message });
                    }
                    break;

                case 'getProgress':
                    try {
                        const progress = this.getProgress();
                        sendResponse({ success: true, data: progress });
                    } catch (error) {
                        console.error('获取进度失败:', error);
                        sendResponse({ success: false, error: error.message });
                    }
                    break;

                case 'getPageInfo':
                    try {
                        const pageInfo = this.getPageInfo();
                        sendResponse({ success: true, data: pageInfo });
                    } catch (error) {
                        console.error('获取页面信息失败:', error);
                        sendResponse({ success: false, error: error.message });
                    }
                    break;

                default:
                    sendResponse({ success: false, error: 'Unknown action: ' + request.action });
            }
        } catch (error) {
            console.error('处理消息时出错:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    async startCollection(config) {
        if (this.isCollecting) {
            throw new Error('采集已在进行中');
        }

        console.log('开始数据采集，配置:', config);

        this.isCollecting = true;
        this.isPaused = false;
        this.config = config;
        this.collectedData = [];
        this.anomalies = [];
        this.processedCount = 0;

        try {
            // 重新分析页面结构
            console.log('分析页面结构...');
            this.analyzePageStructure();

            // 检查页面元素
            if (!this.pageElements) {
                throw new Error('页面元素分析失败');
            }

            console.log('页面元素分析结果:', {
                dropdowns: this.pageElements.dropdowns?.length || 0,
                queryButton: !!this.pageElements.queryButton,
                dataTable: !!this.pageElements.dataTable
            });

            // 即使没有找到下拉框也可以继续，会创建基础任务
            console.log('页面元素检查通过，开始执行采集计划');

            // 异步执行采集计划，不阻塞响应
            this.executeCollectionPlan().catch(error => {
                console.error('采集计划执行失败:', error);
                this.notifyError(error.message);
                this.isCollecting = false;
            });

        } catch (error) {
            console.error('启动采集失败:', error);
            this.isCollecting = false;
            throw error; // 重新抛出错误，让调用者知道启动失败
        }
    }

    async executeCollectionPlan() {
        try {
            console.log('生成采集计划...');

            // 生成采集计划
            const plan = this.generateCollectionPlan();
            this.totalTasks = plan.length;

            console.log(`生成了 ${plan.length} 个采集任务`);

            if (plan.length === 0) {
                throw new Error('没有生成任何采集任务，请检查页面下拉框配置');
            }

            for (let i = 0; i < plan.length && this.isCollecting; i++) {
                if (this.isPaused) {
                    await this.waitForResume();
                }

                const task = plan[i];
                this.currentTask = task.description;
                this.progress = Math.round((i / plan.length) * 100);

                console.log(`执行任务 ${i + 1}/${plan.length}: ${task.description}`);

                try {
                    await this.executeTask(task);
                    this.processedCount++;
                } catch (taskError) {
                    console.error(`任务执行失败:`, taskError);
                    // 继续执行下一个任务
                }

                // 通知进度更新
                this.notifyProgress();

                // 任务间延迟
                await this.delay(1000);
            }

            // 完成后生成报告
            console.log('采集完成，生成报告...');
            await this.generateReport();
        } catch (error) {
            console.error('执行采集计划时出错:', error);
            throw error;
        }
    }

    generateCollectionPlan() {
        try {
            const plan = [];
            const timeRanges = this.generateTimeRanges();

            console.log('配置信息:', this.config);
            console.log('当前页面元素:', this.pageElements);

            // 获取所有需要遍历的选项
            const outlets = this.config.autoTraverseOutlets ?
                this.getDropdownOptions('outlet') : [null];
            const areas = this.config.autoTraverseAreas ?
                this.getDropdownOptions('area') : [null];
            const companies = this.config.autoTraverseCompanies ?
                this.getDropdownOptions('company') : [null];
            const monitorItems = this.config.autoTraverseMonitorItems ?
                this.getDropdownOptions('monitor_item') : [null];

            console.log('遍历选项统计:');
            console.log(`- 排口类型: ${outlets.length} 个 (${this.config.autoTraverseOutlets ? '启用' : '禁用'})`, outlets.slice(0, 3));
            console.log(`- 区域: ${areas.length} 个 (${this.config.autoTraverseAreas ? '启用' : '禁用'})`, areas.slice(0, 3));
            console.log(`- 企业: ${companies.length} 个 (${this.config.autoTraverseCompanies ? '启用' : '禁用'})`, companies.slice(0, 3));
            console.log(`- 监测项目: ${monitorItems.length} 个 (${this.config.autoTraverseMonitorItems ? '启用' : '禁用'})`, monitorItems.slice(0, 3));

            // 检查是否有有效的选项
            const hasValidOutlets = outlets.length > 0 && outlets.some(o => o !== null);
            const hasValidAreas = areas.length > 0 && areas.some(a => a !== null);
            const hasValidCompanies = companies.length > 0 && companies.some(c => c !== null);
            const hasValidMonitorItems = monitorItems.length > 0 && monitorItems.some(m => m !== null);

            console.log('有效选项检查:', {
                hasValidOutlets,
                hasValidAreas,
                hasValidCompanies,
                hasValidMonitorItems
            });

            // 如果所有选项都是null或空数组，创建基础任务
            if (!hasValidOutlets && !hasValidAreas && !hasValidCompanies && !hasValidMonitorItems) {
                console.log('没有找到有效的遍历选项，创建基础查询任务');
                plan.push({
                    timeRange: timeRanges[0],
                    outlet: null,
                    area: null,
                    company: null,
                    monitorItem: null,
                    description: '基础查询（使用页面当前设置）'
                });
            } else {
                // 生成所有组合的查询任务
                timeRanges.forEach(timeRange => {
                    outlets.forEach(outlet => {
                        areas.forEach(area => {
                            companies.forEach(company => {
                                monitorItems.forEach(monitorItem => {
                                    plan.push({
                                        timeRange,
                                        outlet,
                                        area,
                                        company,
                                        monitorItem,
                                        description: this.generateTaskDescription(
                                            timeRange, outlet, area, company, monitorItem
                                        )
                                    });
                                });
                            });
                        });
                    });
                });
            }

            console.log(`生成了 ${plan.length} 个采集任务`);
            return plan;
        } catch (error) {
            console.error('生成采集计划时出错:', error);
            // 返回一个基础任务，确保不会完全失败
            return [{
                timeRange: { type: 'manual', description: '使用页面当前时间设置' },
                outlet: null,
                area: null,
                company: null,
                monitorItem: null,
                description: '基础查询任务'
            }];
        }
    }

    generateTimeRanges() {
        // 不再生成时间范围，用户手动设置时间
        // 返回一个空的时间范围，表示使用当前页面设置的时间
        return [{
            type: 'manual',
            description: '使用页面当前时间设置'
        }];
    }

    getDropdownOptions(type) {
        try {
            if (!this.pageElements || !this.pageElements.dropdowns) {
                console.log('页面元素未初始化');
                return [];
            }

            // 支持多种类型映射
            const typeMapping = {
                'outlet': ['outlet', 'outlet_name'],  // 排口类型和排口名称都算outlet
                'area': ['area'],
                'company': ['company'],
                'monitor_item': ['monitor_item']
            };

            let dropdown = this.pageElements.dropdowns.find(d => d.type === type);

            // 如果没找到精确匹配，尝试映射
            if (!dropdown && typeMapping[type]) {
                for (const mappedType of typeMapping[type]) {
                    dropdown = this.pageElements.dropdowns.find(d => d.type === mappedType);
                    if (dropdown) break;
                }
            }

            if (!dropdown) {
                console.log(`未找到类型为 ${type} 的下拉框`);
                return [];
            }

            // 实时获取Bootstrap Select的选项
            if (dropdown.elementType === 'bootstrap-select' || dropdown.elementType === 'bootstrap-select-button') {
                const freshOptions = this.getBootstrapSelectOptions(dropdown.element);
                if (freshOptions.length > 0) {
                    dropdown.options = freshOptions; // 更新缓存的选项
                }
            }

            const options = dropdown.options.filter(opt => opt.value && opt.value !== '' && opt.value !== '0');
            console.log(`${type} 类型下拉框有 ${options.length} 个有效选项:`, options.slice(0, 3).map(o => o.text));
            return options;
        } catch (error) {
            console.error(`获取 ${type} 下拉框选项时出错:`, error);
            return [];
        }
    }

    generateTaskDescription(timeRange, outlet, area, company, monitorItem) {
        const parts = [];
        if (outlet) parts.push(outlet.text);
        if (area) parts.push(area.text);
        if (company) parts.push(company.text);
        if (monitorItem) parts.push(monitorItem.text);

        const partsStr = parts.length > 0 ? parts.join(' / ') : '全部';
        return `查询 ${partsStr} (使用页面当前时间设置)`;
    }

    async executeTask(task) {
        // 不再设置时间范围，用户已手动设置

        // 设置下拉框选项
        if (task.outlet) await this.setDropdownValue('outlet', task.outlet.value);
        if (task.area) await this.setDropdownValue('area', task.area.value);
        if (task.company) await this.setDropdownValue('company', task.company.value);
        if (task.monitorItem) await this.setDropdownValue('monitor_item', task.monitorItem.value);

        // 执行查询
        await this.performQuery();

        // 等待结果加载
        await this.waitForResults();

        // 收集数据
        const data = await this.collectTableData();

        // 检测异常
        const anomalies = this.detectAnomalies(data, task);

        // 如果发现异常且需要截图
        if (anomalies.length > 0 && this.config.autoScreenshot) {
            await this.takeScreenshot(task, anomalies);
        }

        // 保存数据
        this.collectedData.push(...data);
        this.anomalies.push(...anomalies);
    }



    async setDropdownValue(type, value) {
        const dropdown = this.pageElements.dropdowns.find(d => d.type === type);
        if (!dropdown) {
            console.log(`未找到类型为 ${type} 的下拉框`);
            return;
        }

        try {
            console.log(`设置 ${type} 下拉框值为: ${value}`);

            if (dropdown.elementType === 'bootstrap-select' || dropdown.elementType === 'bootstrap-select-button') {
                // Bootstrap Select下拉框
                await this.setBootstrapSelectValue(dropdown, value);
            } else if (dropdown.elementType === 'select') {
                // 标准select元素
                dropdown.element.value = value;
                dropdown.element.dispatchEvent(new Event('change', { bubbles: true }));
            } else {
                // 其他自定义下拉框
                await this.setCustomDropdownValue(dropdown, value);
            }

            await this.delay(500);
        } catch (error) {
            console.error(`设置 ${type} 下拉框值时出错:`, error);
        }
    }

    async setBootstrapSelectValue(dropdown, value) {
        try {
            const select = dropdown.element;
            const selectId = select.id;

            console.log(`设置Bootstrap Select: ${selectId} = ${value}`);

            // 方法1: 直接设置原始select的值并刷新Bootstrap Select
            select.value = value;

            // 触发Bootstrap Select的刷新
            if (window.$ && window.$.fn.selectpicker) {
                $(select).selectpicker('val', value);
                $(select).selectpicker('refresh');
            }

            // 触发change事件
            select.dispatchEvent(new Event('change', { bubbles: true }));

            // 如果有对应的onchange函数，也要调用
            const onchange = select.getAttribute('onchange');
            if (onchange) {
                try {
                    // 安全地执行onchange函数
                    if (onchange.includes('HistoryReport.selectSubTypeChange')) {
                        window.HistoryReport?.selectSubTypeChange?.(value);
                    } else if (onchange.includes('HistoryReport.selectCityChange')) {
                        window.HistoryReport?.selectCityChange?.(select);
                    } else if (onchange.includes('HistoryReport.selectEntChange')) {
                        window.HistoryReport?.selectEntChange?.();
                    } else if (onchange.includes('HistoryReport.selectSubChange')) {
                        window.HistoryReport?.selectSubChange?.();
                    }
                } catch (error) {
                    console.log('执行onchange函数时出错:', error);
                }
            }

            await this.delay(300);
        } catch (error) {
            console.error('设置Bootstrap Select值时出错:', error);
        }
    }

    async setCustomDropdownValue(dropdown, value) {
        const element = dropdown.element;

        try {
            // 方法1: 尝试点击下拉框打开选项列表
            this.triggerClick(element);
            await this.delay(300);

            // 查找选项列表
            const optionSelectors = [
                `[data-value="${value}"]`,
                `.option[data-value="${value}"]`,
                `.dropdown-item[data-value="${value}"]`,
                `li[data-value="${value}"]`,
                `.layui-select-option[data-value="${value}"]`,
                `.el-select-dropdown__item[data-value="${value}"]`,
                `.ant-select-item[data-value="${value}"]`
            ];

            let optionElement = null;
            for (const selector of optionSelectors) {
                optionElement = document.querySelector(selector);
                if (optionElement) break;
            }

            // 如果没找到精确匹配，尝试文本匹配
            if (!optionElement) {
                const allOptions = document.querySelectorAll('.option, .dropdown-item, li, .select-option');
                for (const option of allOptions) {
                    if (option.textContent?.trim() === value) {
                        optionElement = option;
                        break;
                    }
                }
            }

            if (optionElement) {
                console.log(`找到选项元素，点击选择: ${value}`);
                this.triggerClick(optionElement);
                await this.delay(300);
            } else {
                console.log(`未找到值为 ${value} 的选项`);

                // 方法2: 尝试直接设置文本内容
                if (element.tagName.toLowerCase() === 'input') {
                    element.value = value;
                    element.dispatchEvent(new Event('input', { bubbles: true }));
                    element.dispatchEvent(new Event('change', { bubbles: true }));
                } else {
                    element.textContent = value;
                }
            }
        } catch (error) {
            console.error('设置自定义下拉框值时出错:', error);
        }
    }

    async performQuery() {
        if (!this.pageElements.queryButton) {
            console.log('未找到查询按钮，跳过查询');
            return;
        }

        try {
            console.log('执行查询操作...');
            const button = this.pageElements.queryButton;

            // 尝试多种点击方式
            if (button.tagName.toLowerCase() === 'a') {
                // 对于链接，检查是否有href
                const href = button.getAttribute('href');
                if (href && href !== '#' && !href.startsWith('javascript:')) {
                    // 如果是正常链接，直接导航
                    window.location.href = href;
                } else {
                    // 否则触发点击事件
                    this.triggerClick(button);
                }
            } else {
                // 对于按钮，直接触发点击
                this.triggerClick(button);
            }

            console.log('查询请求已发送，等待响应...');
            // 等待查询完成
            await this.delay(3000);
        } catch (error) {
            console.error('执行查询时出错:', error);
            // 继续执行，不中断流程
        }
    }

    triggerClick(element) {
        try {
            // 方法1: 直接点击
            element.click();
        } catch (error1) {
            try {
                // 方法2: 触发鼠标事件
                const event = new MouseEvent('click', {
                    bubbles: true,
                    cancelable: true,
                    view: window
                });
                element.dispatchEvent(event);
            } catch (error2) {
                try {
                    // 方法3: 触发原生事件
                    const event = document.createEvent('MouseEvents');
                    event.initMouseEvent('click', true, true, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
                    element.dispatchEvent(event);
                } catch (error3) {
                    console.error('所有点击方法都失败了:', error3);
                }
            }
        }
    }

    async waitForResults() {
        // 等待数据加载完成
        let attempts = 0;
        const maxAttempts = 30;

        while (attempts < maxAttempts) {
            const loadingIndicator = document.querySelector('.loading, .spinner, [class*="load"]');
            if (!loadingIndicator || loadingIndicator.style.display === 'none') {
                break;
            }
            await this.delay(1000);
            attempts++;
        }

        // 额外等待确保数据完全加载
        await this.delay(2000);
    }

    async collectTableData() {
        const data = [];
        const table = this.pageElements.dataTable;

        if (!table) return data;

        const headers = Array.from(table.querySelectorAll('th')).map(th => th.textContent.trim());
        const rows = table.querySelectorAll('tbody tr');

        rows.forEach((row, index) => {
            const cells = row.querySelectorAll('td');
            if (cells.length > 0) {
                const rowData = {
                    rowIndex: index,
                    timestamp: new Date().toISOString(),
                    data: {}
                };

                cells.forEach((cell, cellIndex) => {
                    const header = headers[cellIndex] || `column_${cellIndex}`;
                    rowData.data[header] = cell.textContent.trim();
                });

                data.push(rowData);
            }
        });

        return data;
    }

    detectAnomalies(data, task) {
        const anomalies = [];

        data.forEach(row => {
            const anomaly = {
                task: task.description,
                rowIndex: row.rowIndex,
                timestamp: row.timestamp,
                issues: []
            };

            // 检测空值
            if (this.config.detectNullValues) {
                Object.entries(row.data).forEach(([key, value]) => {
                    if (!value || value === '' || value === '-' || value === 'null') {
                        anomaly.issues.push({
                            type: 'null_value',
                            field: key,
                            value: value,
                            description: `字段 ${key} 为空值`
                        });
                    }
                });
            }

            // 检测数值异常
            if (this.config.thresholdValue) {
                Object.entries(row.data).forEach(([key, value]) => {
                    const numValue = parseFloat(value);
                    if (!isNaN(numValue) && numValue > this.config.thresholdValue) {
                        anomaly.issues.push({
                            type: 'threshold_exceeded',
                            field: key,
                            value: numValue,
                            threshold: this.config.thresholdValue,
                            description: `字段 ${key} 值 ${numValue} 超过阈值 ${this.config.thresholdValue}`
                        });
                    }
                });
            }

            // 检测超标数据
            if (this.config.detectExceedance) {
                Object.entries(row.data).forEach(([key, value]) => {
                    if (value && (value.includes('超标') || value.includes('异常') || value.includes('超限'))) {
                        anomaly.issues.push({
                            type: 'exceedance',
                            field: key,
                            value: value,
                            description: `字段 ${key} 检测到超标: ${value}`
                        });
                    }
                });
            }

            if (anomaly.issues.length > 0) {
                anomalies.push(anomaly);
            }
        });

        return anomalies;
    }

    async takeScreenshot(task, anomalies) {
        try {
            // 高亮异常数据
            this.highlightAnomalies(anomalies);

            // 使用Chrome API截图
            const dataUrl = await this.captureScreenshot();

            // 发送截图数据到background script保存
            chrome.runtime.sendMessage({
                action: 'saveScreenshot',
                data: dataUrl,
                filename: this.generateScreenshotFilename(task),
                anomalies: anomalies
            });

            // 移除高亮
            this.removeHighlights();

        } catch (error) {
            console.error('Screenshot failed:', error);
        }
    }

    highlightAnomalies(anomalies) {
        anomalies.forEach(anomaly => {
            const row = this.pageElements.dataTable?.querySelector(`tbody tr:nth-child(${anomaly.rowIndex + 1})`);
            if (row) {
                row.style.backgroundColor = '#ffebee';
                row.style.border = '2px solid #f44336';
                row.classList.add('anomaly-highlight');
            }
        });
    }

    removeHighlights() {
        const highlighted = document.querySelectorAll('.anomaly-highlight');
        highlighted.forEach(element => {
            element.style.backgroundColor = '';
            element.style.border = '';
            element.classList.remove('anomaly-highlight');
        });
    }

    async captureScreenshot() {
        return new Promise((resolve, reject) => {
            chrome.runtime.sendMessage({
                action: 'captureTab'
            }, (response) => {
                if (response && response.dataUrl) {
                    resolve(response.dataUrl);
                } else {
                    reject(new Error('截图失败'));
                }
            });
        });
    }

    generateScreenshotFilename(task) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const taskName = task.description.replace(/[^\w\s-]/g, '').replace(/\s+/g, '_');
        return `anomaly_${timestamp}_${taskName}.png`;
    }



    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async waitForResume() {
        while (this.isPaused && this.isCollecting) {
            await this.delay(1000);
        }
    }

    stopCollection() {
        this.isCollecting = false;
        this.isPaused = false;
    }

    pauseCollection() {
        this.isPaused = !this.isPaused;
    }

    getProgress() {
        return {
            isCollecting: this.isCollecting,
            isPaused: this.isPaused,
            progress: this.progress,
            currentTask: this.currentTask,
            processedCount: this.processedCount,
            totalTasks: this.totalTasks,
            anomalyCount: this.anomalies.length,
            screenshotCount: this.anomalies.filter(a => a.screenshot).length
        };
    }

    getPageInfo() {
        return {
            isEnvironmentSystem: this.isEnvironmentSystem,
            url: window.location.href,
            title: document.title,
            elementCount: Object.keys(this.pageElements).length,
            hasDropdowns: this.pageElements.dropdowns?.length > 0,
            hasQueryButton: !!this.pageElements.queryButton,
            hasDataTable: !!this.pageElements.dataTable
        };
    }

    notifyProgress() {
        chrome.runtime.sendMessage({
            action: 'progressUpdate',
            progress: this.getProgress()
        });
    }

    notifyError(message) {
        chrome.runtime.sendMessage({
            action: 'error',
            message: message
        });
    }

    async generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            config: this.config,
            summary: {
                totalTasks: this.totalTasks,
                processedCount: this.processedCount,
                dataCollected: this.collectedData.length,
                anomaliesFound: this.anomalies.length
            },
            data: this.collectedData,
            anomalies: this.anomalies
        };

        chrome.runtime.sendMessage({
            action: 'saveReport',
            report: report
        });
    }
}

// 初始化采集器
const collector = new EnvironmentDataCollector();
