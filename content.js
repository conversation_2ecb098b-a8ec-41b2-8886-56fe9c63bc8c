// 环境监测数据自动采集器 - Content Script
class EnvironmentDataCollector {
    constructor() {
        this.isCollecting = false;
        this.isPaused = false;
        this.config = {};
        this.collectedData = [];
        this.anomalies = [];
        this.currentTask = '';
        this.progress = 0;
        this.totalTasks = 0;
        this.processedCount = 0;
        this.isEnvironmentSystem = false;
        this.pageElements = {
            dropdowns: [],
            queryButton: null,
            dataTable: null,
            pagination: null
        };

        this.init();
    }

    init() {
        // 监听来自popup的消息
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            // 对于异步操作，需要返回true来保持消息通道开放
            this.handleMessage(request, sender, sendResponse);
            return true; // 保持消息通道开放
        });

        // 检测页面是否为环境监测系统
        this.detectEnvironmentSystem();

        console.log('环境监测数据采集器已初始化');
    }

    detectEnvironmentSystem() {
        // 检测页面特征，判断是否为环境监测系统
        const indicators = [
            '排口类型', '所属区域', '企业名称', '排口名称', '监测项目',
            '实时数据', '历史数据', '环境监测', '污染源'
        ];

        const pageText = document.body.innerText;
        const matchCount = indicators.filter(indicator =>
            pageText.includes(indicator)
        ).length;

        if (matchCount >= 3) {
            this.isEnvironmentSystem = true;
            this.analyzePageStructure();
        } else {
            // 即使不是环境监测系统，也要初始化页面元素
            this.analyzePageStructure();
        }
    }

    analyzePageStructure() {
        try {
            // 分析页面结构，识别关键元素
            this.pageElements = {
                dropdowns: this.findDropdowns() || [],
                queryButton: this.findQueryButton() || null,
                dataTable: this.findDataTable() || null,
                pagination: this.findPagination() || null
            };

            console.log('页面元素分析完成:', this.pageElements);
        } catch (error) {
            console.error('页面结构分析失败:', error);
            // 确保pageElements有默认值
            this.pageElements = {
                dropdowns: [],
                queryButton: null,
                dataTable: null,
                pagination: null
            };
        }
    }



    findDropdowns() {
        try {
            // 查找下拉框（排口类型、区域、企业等）
            const dropdowns = [];
            const selects = document.querySelectorAll('select');

            console.log(`找到 ${selects.length} 个下拉框`);

            selects.forEach((select, index) => {
                try {
                    const label = this.getInputLabel(select);
                    const id = select.id || '';
                    const name = select.name || '';

                    // 更宽泛的匹配条件
                    let type = 'unknown';
                    if (label && (label.includes('排口') || label.includes('出口'))) {
                        type = 'outlet';
                    } else if (label && (label.includes('区域') || label.includes('地区'))) {
                        type = 'area';
                    } else if (label && (label.includes('企业') || label.includes('公司'))) {
                        type = 'company';
                    } else if (label && (label.includes('监测') || label.includes('项目'))) {
                        type = 'monitor_item';
                    } else if (id.includes('outlet') || name.includes('outlet')) {
                        type = 'outlet';
                    } else if (id.includes('area') || name.includes('area')) {
                        type = 'area';
                    } else if (id.includes('company') || name.includes('company')) {
                        type = 'company';
                    } else if (id.includes('monitor') || name.includes('monitor')) {
                        type = 'monitor_item';
                    }

                    const options = Array.from(select.options).map(opt => ({
                        value: opt.value,
                        text: opt.text.trim()
                    })).filter(opt => opt.value && opt.text);

                    dropdowns.push({
                        element: select,
                        label: label || `下拉框${index + 1}`,
                        type: type,
                        id: id,
                        name: name,
                        options: options
                    });

                    console.log(`下拉框 ${index + 1}: ${label || id || name}, 类型: ${type}, 选项数: ${options.length}`);
                } catch (error) {
                    console.error(`处理下拉框 ${index} 时出错:`, error);
                }
            });

            return dropdowns;
        } catch (error) {
            console.error('查找下拉框时出错:', error);
            return [];
        }
    }

    findQueryButton() {
        try {
            // 查找查询按钮
            const buttons = document.querySelectorAll('button, input[type="button"], input[type="submit"], a');

            console.log(`找到 ${buttons.length} 个可能的按钮`);

            for (let button of buttons) {
                const text = button.textContent || button.value || '';
                const id = button.id || '';
                const className = button.className || '';

                if (text.includes('查询') || text.includes('搜索') || text.includes('检索') ||
                    text.includes('Query') || text.includes('Search') ||
                    id.includes('query') || id.includes('search') ||
                    className.includes('query') || className.includes('search')) {
                    console.log('找到查询按钮:', text || id);
                    return button;
                }
            }

            console.log('未找到查询按钮');
            return null;
        } catch (error) {
            console.error('查找查询按钮时出错:', error);
            return null;
        }
    }

    findDataTable() {
        try {
            // 查找数据表格
            const tables = document.querySelectorAll('table');

            console.log(`找到 ${tables.length} 个表格`);

            for (let table of tables) {
                const headers = table.querySelectorAll('th');
                const rows = table.querySelectorAll('tr');

                // 降低要求：至少有3列或者至少有5行数据
                if (headers.length >= 3 || rows.length >= 5) {
                    console.log(`找到数据表格: ${headers.length} 列, ${rows.length} 行`);
                    return table;
                }
            }

            // 如果没找到标准表格，查找其他可能的数据容器
            const dataContainers = document.querySelectorAll('.data-table, .grid, .datagrid, [class*="table"]');
            if (dataContainers.length > 0) {
                console.log('找到数据容器:', dataContainers[0].className);
                return dataContainers[0];
            }

            console.log('未找到数据表格');
            return null;
        } catch (error) {
            console.error('查找数据表格时出错:', error);
            return null;
        }
    }

    findPagination() {
        // 查找分页元素
        const paginationSelectors = [
            '.pagination', '.pager', '.page-nav',
            '[class*="page"]', '[class*="paging"]'
        ];
        
        for (let selector of paginationSelectors) {
            const element = document.querySelector(selector);
            if (element) return element;
        }
        
        return null;
    }

    getInputLabel(input) {
        // 获取输入框的标签文本
        const id = input.id;
        if (id) {
            const label = document.querySelector(`label[for="${id}"]`);
            if (label) return label.textContent.trim();
        }
        
        // 查找前面的文本
        const parent = input.parentElement;
        if (parent) {
            const text = parent.textContent.replace(input.value || '', '').trim();
            if (text.length > 0 && text.length < 20) {
                return text;
            }
        }
        
        return null;
    }

    async handleMessage(request, sender, sendResponse) {
        try {
            console.log('收到消息:', request.action);

            switch (request.action) {
                case 'ping':
                    sendResponse({ success: true, message: 'Content script is ready' });
                    break;

                case 'startCollection':
                    try {
                        await this.startCollection(request.config);
                        sendResponse({ success: true, message: '采集已开始' });
                    } catch (error) {
                        console.error('启动采集失败:', error);
                        sendResponse({ success: false, error: error.message });
                    }
                    break;

                case 'stopCollection':
                    try {
                        this.stopCollection();
                        sendResponse({ success: true, message: '采集已停止' });
                    } catch (error) {
                        console.error('停止采集失败:', error);
                        sendResponse({ success: false, error: error.message });
                    }
                    break;

                case 'pauseCollection':
                    try {
                        this.pauseCollection();
                        sendResponse({ success: true, message: '采集已暂停' });
                    } catch (error) {
                        console.error('暂停采集失败:', error);
                        sendResponse({ success: false, error: error.message });
                    }
                    break;

                case 'getProgress':
                    try {
                        const progress = this.getProgress();
                        sendResponse({ success: true, data: progress });
                    } catch (error) {
                        console.error('获取进度失败:', error);
                        sendResponse({ success: false, error: error.message });
                    }
                    break;

                case 'getPageInfo':
                    try {
                        const pageInfo = this.getPageInfo();
                        sendResponse({ success: true, data: pageInfo });
                    } catch (error) {
                        console.error('获取页面信息失败:', error);
                        sendResponse({ success: false, error: error.message });
                    }
                    break;

                default:
                    sendResponse({ success: false, error: 'Unknown action: ' + request.action });
            }
        } catch (error) {
            console.error('处理消息时出错:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    async startCollection(config) {
        if (this.isCollecting) {
            throw new Error('采集已在进行中');
        }

        console.log('开始数据采集，配置:', config);

        this.isCollecting = true;
        this.isPaused = false;
        this.config = config;
        this.collectedData = [];
        this.anomalies = [];
        this.processedCount = 0;

        try {
            // 重新分析页面结构
            console.log('分析页面结构...');
            this.analyzePageStructure();

            // 检查页面元素
            if (!this.pageElements) {
                throw new Error('页面元素分析失败');
            }

            console.log('页面元素分析结果:', {
                dropdowns: this.pageElements.dropdowns?.length || 0,
                queryButton: !!this.pageElements.queryButton,
                dataTable: !!this.pageElements.dataTable
            });

            // 即使没有找到下拉框也可以继续，会创建基础任务
            console.log('页面元素检查通过，开始执行采集计划');

            // 异步执行采集计划，不阻塞响应
            this.executeCollectionPlan().catch(error => {
                console.error('采集计划执行失败:', error);
                this.notifyError(error.message);
                this.isCollecting = false;
            });

        } catch (error) {
            console.error('启动采集失败:', error);
            this.isCollecting = false;
            throw error; // 重新抛出错误，让调用者知道启动失败
        }
    }

    async executeCollectionPlan() {
        try {
            console.log('生成采集计划...');

            // 生成采集计划
            const plan = this.generateCollectionPlan();
            this.totalTasks = plan.length;

            console.log(`生成了 ${plan.length} 个采集任务`);

            if (plan.length === 0) {
                throw new Error('没有生成任何采集任务，请检查页面下拉框配置');
            }

            for (let i = 0; i < plan.length && this.isCollecting; i++) {
                if (this.isPaused) {
                    await this.waitForResume();
                }

                const task = plan[i];
                this.currentTask = task.description;
                this.progress = Math.round((i / plan.length) * 100);

                console.log(`执行任务 ${i + 1}/${plan.length}: ${task.description}`);

                try {
                    await this.executeTask(task);
                    this.processedCount++;
                } catch (taskError) {
                    console.error(`任务执行失败:`, taskError);
                    // 继续执行下一个任务
                }

                // 通知进度更新
                this.notifyProgress();

                // 任务间延迟
                await this.delay(1000);
            }

            // 完成后生成报告
            console.log('采集完成，生成报告...');
            await this.generateReport();
        } catch (error) {
            console.error('执行采集计划时出错:', error);
            throw error;
        }
    }

    generateCollectionPlan() {
        try {
            const plan = [];
            const timeRanges = this.generateTimeRanges();

            console.log('配置信息:', this.config);

            // 获取所有需要遍历的选项
            const outlets = this.config.autoTraverseOutlets ?
                this.getDropdownOptions('outlet') : [null];
            const areas = this.config.autoTraverseAreas ?
                this.getDropdownOptions('area') : [null];
            const companies = this.config.autoTraverseCompanies ?
                this.getDropdownOptions('company') : [null];
            const monitorItems = this.config.autoTraverseMonitorItems ?
                this.getDropdownOptions('monitor_item') : [null];

            console.log('遍历选项统计:');
            console.log(`- 排口类型: ${outlets.length} 个 (${this.config.autoTraverseOutlets ? '启用' : '禁用'})`);
            console.log(`- 区域: ${areas.length} 个 (${this.config.autoTraverseAreas ? '启用' : '禁用'})`);
            console.log(`- 企业: ${companies.length} 个 (${this.config.autoTraverseCompanies ? '启用' : '禁用'})`);
            console.log(`- 监测项目: ${monitorItems.length} 个 (${this.config.autoTraverseMonitorItems ? '启用' : '禁用'})`);

            // 如果所有选项都是null（即都没有启用遍历），至少创建一个基础任务
            if (outlets.every(o => o === null) && areas.every(a => a === null) &&
                companies.every(c => c === null) && monitorItems.every(m => m === null)) {
                console.log('所有遍历选项都未启用，创建基础查询任务');
                plan.push({
                    timeRange: timeRanges[0],
                    outlet: null,
                    area: null,
                    company: null,
                    monitorItem: null,
                    description: '基础查询（使用页面当前设置）'
                });
            } else {
                // 生成所有组合的查询任务
                timeRanges.forEach(timeRange => {
                    outlets.forEach(outlet => {
                        areas.forEach(area => {
                            companies.forEach(company => {
                                monitorItems.forEach(monitorItem => {
                                    plan.push({
                                        timeRange,
                                        outlet,
                                        area,
                                        company,
                                        monitorItem,
                                        description: this.generateTaskDescription(
                                            timeRange, outlet, area, company, monitorItem
                                        )
                                    });
                                });
                            });
                        });
                    });
                });
            }

            console.log(`生成了 ${plan.length} 个采集任务`);
            return plan;
        } catch (error) {
            console.error('生成采集计划时出错:', error);
            // 返回一个基础任务，确保不会完全失败
            return [{
                timeRange: { type: 'manual', description: '使用页面当前时间设置' },
                outlet: null,
                area: null,
                company: null,
                monitorItem: null,
                description: '基础查询任务'
            }];
        }
    }

    generateTimeRanges() {
        // 不再生成时间范围，用户手动设置时间
        // 返回一个空的时间范围，表示使用当前页面设置的时间
        return [{
            type: 'manual',
            description: '使用页面当前时间设置'
        }];
    }

    getDropdownOptions(type) {
        try {
            if (!this.pageElements || !this.pageElements.dropdowns) {
                console.log('页面元素未初始化');
                return [];
            }

            const dropdown = this.pageElements.dropdowns.find(d => d.type === type);
            if (!dropdown) {
                console.log(`未找到类型为 ${type} 的下拉框`);
                return [];
            }

            const options = dropdown.options.filter(opt => opt.value && opt.value !== '');
            console.log(`${type} 类型下拉框有 ${options.length} 个有效选项`);
            return options;
        } catch (error) {
            console.error(`获取 ${type} 下拉框选项时出错:`, error);
            return [];
        }
    }

    generateTaskDescription(timeRange, outlet, area, company, monitorItem) {
        const parts = [];
        if (outlet) parts.push(outlet.text);
        if (area) parts.push(area.text);
        if (company) parts.push(company.text);
        if (monitorItem) parts.push(monitorItem.text);

        const partsStr = parts.length > 0 ? parts.join(' / ') : '全部';
        return `查询 ${partsStr} (使用页面当前时间设置)`;
    }

    async executeTask(task) {
        // 不再设置时间范围，用户已手动设置

        // 设置下拉框选项
        if (task.outlet) await this.setDropdownValue('outlet', task.outlet.value);
        if (task.area) await this.setDropdownValue('area', task.area.value);
        if (task.company) await this.setDropdownValue('company', task.company.value);
        if (task.monitorItem) await this.setDropdownValue('monitor_item', task.monitorItem.value);

        // 执行查询
        await this.performQuery();

        // 等待结果加载
        await this.waitForResults();

        // 收集数据
        const data = await this.collectTableData();

        // 检测异常
        const anomalies = this.detectAnomalies(data, task);

        // 如果发现异常且需要截图
        if (anomalies.length > 0 && this.config.autoScreenshot) {
            await this.takeScreenshot(task, anomalies);
        }

        // 保存数据
        this.collectedData.push(...data);
        this.anomalies.push(...anomalies);
    }



    async setDropdownValue(type, value) {
        const dropdown = this.pageElements.dropdowns.find(d => d.type === type);
        if (dropdown && dropdown.element) {
            dropdown.element.value = value;
            dropdown.element.dispatchEvent(new Event('change', { bubbles: true }));
            await this.delay(300);
        }
    }

    async performQuery() {
        if (this.pageElements.queryButton) {
            this.pageElements.queryButton.click();
            await this.delay(1000);
        }
    }

    async waitForResults() {
        // 等待数据加载完成
        let attempts = 0;
        const maxAttempts = 30;

        while (attempts < maxAttempts) {
            const loadingIndicator = document.querySelector('.loading, .spinner, [class*="load"]');
            if (!loadingIndicator || loadingIndicator.style.display === 'none') {
                break;
            }
            await this.delay(1000);
            attempts++;
        }

        // 额外等待确保数据完全加载
        await this.delay(2000);
    }

    async collectTableData() {
        const data = [];
        const table = this.pageElements.dataTable;

        if (!table) return data;

        const headers = Array.from(table.querySelectorAll('th')).map(th => th.textContent.trim());
        const rows = table.querySelectorAll('tbody tr');

        rows.forEach((row, index) => {
            const cells = row.querySelectorAll('td');
            if (cells.length > 0) {
                const rowData = {
                    rowIndex: index,
                    timestamp: new Date().toISOString(),
                    data: {}
                };

                cells.forEach((cell, cellIndex) => {
                    const header = headers[cellIndex] || `column_${cellIndex}`;
                    rowData.data[header] = cell.textContent.trim();
                });

                data.push(rowData);
            }
        });

        return data;
    }

    detectAnomalies(data, task) {
        const anomalies = [];

        data.forEach(row => {
            const anomaly = {
                task: task.description,
                rowIndex: row.rowIndex,
                timestamp: row.timestamp,
                issues: []
            };

            // 检测空值
            if (this.config.detectNullValues) {
                Object.entries(row.data).forEach(([key, value]) => {
                    if (!value || value === '' || value === '-' || value === 'null') {
                        anomaly.issues.push({
                            type: 'null_value',
                            field: key,
                            value: value,
                            description: `字段 ${key} 为空值`
                        });
                    }
                });
            }

            // 检测数值异常
            if (this.config.thresholdValue) {
                Object.entries(row.data).forEach(([key, value]) => {
                    const numValue = parseFloat(value);
                    if (!isNaN(numValue) && numValue > this.config.thresholdValue) {
                        anomaly.issues.push({
                            type: 'threshold_exceeded',
                            field: key,
                            value: numValue,
                            threshold: this.config.thresholdValue,
                            description: `字段 ${key} 值 ${numValue} 超过阈值 ${this.config.thresholdValue}`
                        });
                    }
                });
            }

            // 检测超标数据
            if (this.config.detectExceedance) {
                Object.entries(row.data).forEach(([key, value]) => {
                    if (value && (value.includes('超标') || value.includes('异常') || value.includes('超限'))) {
                        anomaly.issues.push({
                            type: 'exceedance',
                            field: key,
                            value: value,
                            description: `字段 ${key} 检测到超标: ${value}`
                        });
                    }
                });
            }

            if (anomaly.issues.length > 0) {
                anomalies.push(anomaly);
            }
        });

        return anomalies;
    }

    async takeScreenshot(task, anomalies) {
        try {
            // 高亮异常数据
            this.highlightAnomalies(anomalies);

            // 使用Chrome API截图
            const dataUrl = await this.captureScreenshot();

            // 发送截图数据到background script保存
            chrome.runtime.sendMessage({
                action: 'saveScreenshot',
                data: dataUrl,
                filename: this.generateScreenshotFilename(task),
                anomalies: anomalies
            });

            // 移除高亮
            this.removeHighlights();

        } catch (error) {
            console.error('Screenshot failed:', error);
        }
    }

    highlightAnomalies(anomalies) {
        anomalies.forEach(anomaly => {
            const row = this.pageElements.dataTable?.querySelector(`tbody tr:nth-child(${anomaly.rowIndex + 1})`);
            if (row) {
                row.style.backgroundColor = '#ffebee';
                row.style.border = '2px solid #f44336';
                row.classList.add('anomaly-highlight');
            }
        });
    }

    removeHighlights() {
        const highlighted = document.querySelectorAll('.anomaly-highlight');
        highlighted.forEach(element => {
            element.style.backgroundColor = '';
            element.style.border = '';
            element.classList.remove('anomaly-highlight');
        });
    }

    async captureScreenshot() {
        return new Promise((resolve, reject) => {
            chrome.runtime.sendMessage({
                action: 'captureTab'
            }, (response) => {
                if (response && response.dataUrl) {
                    resolve(response.dataUrl);
                } else {
                    reject(new Error('截图失败'));
                }
            });
        });
    }

    generateScreenshotFilename(task) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const taskName = task.description.replace(/[^\w\s-]/g, '').replace(/\s+/g, '_');
        return `anomaly_${timestamp}_${taskName}.png`;
    }



    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async waitForResume() {
        while (this.isPaused && this.isCollecting) {
            await this.delay(1000);
        }
    }

    stopCollection() {
        this.isCollecting = false;
        this.isPaused = false;
    }

    pauseCollection() {
        this.isPaused = !this.isPaused;
    }

    getProgress() {
        return {
            isCollecting: this.isCollecting,
            isPaused: this.isPaused,
            progress: this.progress,
            currentTask: this.currentTask,
            processedCount: this.processedCount,
            totalTasks: this.totalTasks,
            anomalyCount: this.anomalies.length,
            screenshotCount: this.anomalies.filter(a => a.screenshot).length
        };
    }

    getPageInfo() {
        return {
            isEnvironmentSystem: this.isEnvironmentSystem,
            url: window.location.href,
            title: document.title,
            elementCount: Object.keys(this.pageElements).length,
            hasDropdowns: this.pageElements.dropdowns?.length > 0,
            hasQueryButton: !!this.pageElements.queryButton,
            hasDataTable: !!this.pageElements.dataTable
        };
    }

    notifyProgress() {
        chrome.runtime.sendMessage({
            action: 'progressUpdate',
            progress: this.getProgress()
        });
    }

    notifyError(message) {
        chrome.runtime.sendMessage({
            action: 'error',
            message: message
        });
    }

    async generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            config: this.config,
            summary: {
                totalTasks: this.totalTasks,
                processedCount: this.processedCount,
                dataCollected: this.collectedData.length,
                anomaliesFound: this.anomalies.length
            },
            data: this.collectedData,
            anomalies: this.anomalies
        };

        chrome.runtime.sendMessage({
            action: 'saveReport',
            report: report
        });
    }
}

// 初始化采集器
const collector = new EnvironmentDataCollector();
