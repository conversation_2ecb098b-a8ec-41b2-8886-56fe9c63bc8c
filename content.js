// 环境监测数据自动采集器 - Content Script
class EnvironmentDataCollector {
    constructor() {
        this.isCollecting = false;
        this.isPaused = false;
        this.config = {};
        this.collectedData = [];
        this.anomalies = [];
        this.currentTask = '';
        this.progress = 0;
        this.totalTasks = 0;
        this.processedCount = 0;
        
        this.init();
    }

    init() {
        // 监听来自popup的消息
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
        });

        // 检测页面是否为环境监测系统
        this.detectEnvironmentSystem();
    }

    detectEnvironmentSystem() {
        // 检测页面特征，判断是否为环境监测系统
        const indicators = [
            '排口类型', '所属区域', '企业名称', '排口名称', '监测项目',
            '实时数据', '历史数据', '环境监测', '污染源'
        ];
        
        const pageText = document.body.innerText;
        const matchCount = indicators.filter(indicator => 
            pageText.includes(indicator)
        ).length;

        if (matchCount >= 3) {
            this.isEnvironmentSystem = true;
            this.analyzePageStructure();
        }
    }

    analyzePageStructure() {
        // 分析页面结构，识别关键元素
        this.pageElements = {
            timeInputs: this.findTimeInputs(),
            dropdowns: this.findDropdowns(),
            queryButton: this.findQueryButton(),
            dataTable: this.findDataTable(),
            pagination: this.findPagination()
        };
    }

    findTimeInputs() {
        // 查找页面中的时间控件
        const timeInputs = [];

        // 查找所有可能的时间输入框
        const allInputs = document.querySelectorAll('input[type="text"], input[type="datetime-local"], input[type="date"], input[type="time"]');

        allInputs.forEach(input => {
            const label = this.getInputLabel(input);
            const placeholder = input.placeholder || '';
            const id = input.id || '';
            const name = input.name || '';

            // 通过多种方式识别时间输入框
            const timeKeywords = ['时间', '日期', 'time', 'date', '开始', '结束', 'start', 'end'];
            const isTimeInput = timeKeywords.some(keyword =>
                (label && label.includes(keyword)) ||
                placeholder.includes(keyword) ||
                id.toLowerCase().includes(keyword.toLowerCase()) ||
                name.toLowerCase().includes(keyword.toLowerCase())
            );

            if (isTimeInput) {
                let type = 'unknown';
                if (label && (label.includes('开始') || id.includes('start') || name.includes('start'))) {
                    type = 'start';
                } else if (label && (label.includes('结束') || id.includes('end') || name.includes('end'))) {
                    type = 'end';
                }

                timeInputs.push({
                    element: input,
                    label: label || placeholder || id,
                    type: type,
                    id: id,
                    name: name
                });
            }
        });

        // 查找小时选择下拉框
        const hourSelects = document.querySelectorAll('select');
        hourSelects.forEach(select => {
            const label = this.getInputLabel(select);
            const id = select.id || '';
            const name = select.name || '';

            // 识别小时选择框
            if ((label && label.includes('小时')) ||
                id.includes('hour') || name.includes('hour') ||
                id.includes('小时') || name.includes('小时')) {
                timeInputs.push({
                    element: select,
                    label: label || '小时选择',
                    type: 'hour',
                    id: id,
                    name: name,
                    options: Array.from(select.options).map(opt => ({
                        value: opt.value,
                        text: opt.text
                    }))
                });
            }
        });

        return timeInputs;
    }

    findDropdowns() {
        // 查找下拉框（排口类型、区域、企业等）
        const dropdowns = [];
        const selects = document.querySelectorAll('select');
        
        selects.forEach(select => {
            const label = this.getInputLabel(select);
            if (label) {
                let type = 'unknown';
                if (label.includes('排口')) type = 'outlet';
                else if (label.includes('区域')) type = 'area';
                else if (label.includes('企业')) type = 'company';
                else if (label.includes('监测项目')) type = 'monitor_item';
                
                dropdowns.push({
                    element: select,
                    label: label,
                    type: type,
                    options: Array.from(select.options).map(opt => ({
                        value: opt.value,
                        text: opt.text
                    }))
                });
            }
        });
        
        return dropdowns;
    }

    findQueryButton() {
        // 查找查询按钮
        const buttons = document.querySelectorAll('button, input[type="button"], input[type="submit"]');
        
        for (let button of buttons) {
            const text = button.textContent || button.value || '';
            if (text.includes('查询') || text.includes('搜索') || text.includes('检索')) {
                return button;
            }
        }
        
        return null;
    }

    findDataTable() {
        // 查找数据表格
        const tables = document.querySelectorAll('table');
        
        for (let table of tables) {
            const headers = table.querySelectorAll('th');
            if (headers.length > 5) { // 假设数据表至少有5列
                return table;
            }
        }
        
        return null;
    }

    findPagination() {
        // 查找分页元素
        const paginationSelectors = [
            '.pagination', '.pager', '.page-nav',
            '[class*="page"]', '[class*="paging"]'
        ];
        
        for (let selector of paginationSelectors) {
            const element = document.querySelector(selector);
            if (element) return element;
        }
        
        return null;
    }

    getInputLabel(input) {
        // 获取输入框的标签文本
        const id = input.id;
        if (id) {
            const label = document.querySelector(`label[for="${id}"]`);
            if (label) return label.textContent.trim();
        }
        
        // 查找前面的文本
        const parent = input.parentElement;
        if (parent) {
            const text = parent.textContent.replace(input.value || '', '').trim();
            if (text.length > 0 && text.length < 20) {
                return text;
            }
        }
        
        return null;
    }

    async handleMessage(request, sender, sendResponse) {
        switch (request.action) {
            case 'startCollection':
                await this.startCollection(request.config);
                sendResponse({ success: true });
                break;
                
            case 'stopCollection':
                this.stopCollection();
                sendResponse({ success: true });
                break;
                
            case 'pauseCollection':
                this.pauseCollection();
                sendResponse({ success: true });
                break;
                
            case 'getProgress':
                sendResponse(this.getProgress());
                break;
                
            case 'getPageInfo':
                sendResponse(this.getPageInfo());
                break;
                
            default:
                sendResponse({ error: 'Unknown action' });
        }
    }

    async startCollection(config) {
        if (this.isCollecting) return;
        
        this.isCollecting = true;
        this.isPaused = false;
        this.config = config;
        this.collectedData = [];
        this.anomalies = [];
        this.processedCount = 0;
        
        try {
            await this.executeCollectionPlan();
        } catch (error) {
            console.error('Collection error:', error);
            this.notifyError(error.message);
        } finally {
            this.isCollecting = false;
        }
    }

    async executeCollectionPlan() {
        // 生成采集计划
        const plan = this.generateCollectionPlan();
        this.totalTasks = plan.length;
        
        for (let i = 0; i < plan.length && this.isCollecting; i++) {
            if (this.isPaused) {
                await this.waitForResume();
            }
            
            const task = plan[i];
            this.currentTask = task.description;
            this.progress = Math.round((i / plan.length) * 100);
            
            await this.executeTask(task);
            this.processedCount++;
            
            // 通知进度更新
            this.notifyProgress();
            
            // 任务间延迟
            await this.delay(1000);
        }
        
        // 完成后生成报告
        await this.generateReport();
    }

    generateCollectionPlan() {
        const plan = [];
        const timeRanges = this.generateTimeRanges();
        
        // 获取所有需要遍历的选项
        const outlets = this.config.autoTraverseOutlets ? 
            this.getDropdownOptions('outlet') : [null];
        const areas = this.config.autoTraverseAreas ? 
            this.getDropdownOptions('area') : [null];
        const companies = this.config.autoTraverseCompanies ? 
            this.getDropdownOptions('company') : [null];
        const monitorItems = this.config.autoTraverseMonitorItems ? 
            this.getDropdownOptions('monitor_item') : [null];
        
        // 生成所有组合的查询任务
        timeRanges.forEach(timeRange => {
            outlets.forEach(outlet => {
                areas.forEach(area => {
                    companies.forEach(company => {
                        monitorItems.forEach(monitorItem => {
                            plan.push({
                                timeRange,
                                outlet,
                                area,
                                company,
                                monitorItem,
                                description: this.generateTaskDescription(
                                    timeRange, outlet, area, company, monitorItem
                                )
                            });
                        });
                    });
                });
            });
        });
        
        return plan;
    }

    generateTimeRanges() {
        const ranges = [];

        // 如果页面有小时选择框，使用页面的小时选项
        const hourSelect = this.pageElements.timeInputs.find(t => t.type === 'hour');
        if (hourSelect && hourSelect.options) {
            // 使用页面中的小时选项
            hourSelect.options.forEach(option => {
                if (option.value) {
                    ranges.push({
                        hourValue: option.value,
                        hourText: option.text,
                        type: 'hour_option'
                    });
                }
            });
        }

        // 如果配置了自定义时间范围，也添加进去
        if (this.config.startTime && this.config.endTime) {
            const start = new Date(this.config.startTime);
            const end = new Date(this.config.endTime);
            const interval = (this.config.queryInterval || 1) * 60 * 60 * 1000;

            let current = new Date(start);
            while (current < end) {
                const rangeEnd = new Date(Math.min(current.getTime() + interval, end.getTime()));
                ranges.push({
                    start: new Date(current),
                    end: new Date(rangeEnd),
                    type: 'time_range'
                });
                current = new Date(rangeEnd);
            }
        }

        // 如果没有任何时间配置，创建一个默认的当前时间范围
        if (ranges.length === 0) {
            const now = new Date();
            const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
            ranges.push({
                start: oneHourAgo,
                end: now,
                type: 'default'
            });
        }

        return ranges;
    }

    getDropdownOptions(type) {
        const dropdown = this.pageElements.dropdowns.find(d => d.type === type);
        return dropdown ? dropdown.options.filter(opt => opt.value) : [];
    }

    generateTaskDescription(timeRange, outlet, area, company, monitorItem) {
        const parts = [];
        if (outlet) parts.push(outlet.text);
        if (area) parts.push(area.text);
        if (company) parts.push(company.text);
        if (monitorItem) parts.push(monitorItem.text);
        
        const timeStr = `${timeRange.start.toLocaleString()} - ${timeRange.end.toLocaleString()}`;
        return `查询 ${parts.join(' / ')} (${timeStr})`;
    }

    async executeTask(task) {
        // 设置时间范围
        await this.setTimeRange(task.timeRange);
        
        // 设置下拉框选项
        if (task.outlet) await this.setDropdownValue('outlet', task.outlet.value);
        if (task.area) await this.setDropdownValue('area', task.area.value);
        if (task.company) await this.setDropdownValue('company', task.company.value);
        if (task.monitorItem) await this.setDropdownValue('monitor_item', task.monitorItem.value);
        
        // 执行查询
        await this.performQuery();
        
        // 等待结果加载
        await this.waitForResults();
        
        // 收集数据
        const data = await this.collectTableData();
        
        // 检测异常
        const anomalies = this.detectAnomalies(data, task);
        
        // 如果发现异常且需要截图
        if (anomalies.length > 0 && this.config.autoScreenshot) {
            await this.takeScreenshot(task, anomalies);
        }
        
        // 保存数据
        this.collectedData.push(...data);
        this.anomalies.push(...anomalies);
    }

    async setTimeRange(timeRange) {
        const startInput = this.pageElements.timeInputs.find(t => t.type === 'start');
        const endInput = this.pageElements.timeInputs.find(t => t.type === 'end');

        if (startInput) {
            startInput.element.value = this.formatDateTime(timeRange.start);
            startInput.element.dispatchEvent(new Event('change', { bubbles: true }));
        }

        if (endInput) {
            endInput.element.value = this.formatDateTime(timeRange.end);
            endInput.element.dispatchEvent(new Event('change', { bubbles: true }));
        }

        await this.delay(500);
    }

    async setDropdownValue(type, value) {
        const dropdown = this.pageElements.dropdowns.find(d => d.type === type);
        if (dropdown && dropdown.element) {
            dropdown.element.value = value;
            dropdown.element.dispatchEvent(new Event('change', { bubbles: true }));
            await this.delay(300);
        }
    }

    async performQuery() {
        if (this.pageElements.queryButton) {
            this.pageElements.queryButton.click();
            await this.delay(1000);
        }
    }

    async waitForResults() {
        // 等待数据加载完成
        let attempts = 0;
        const maxAttempts = 30;

        while (attempts < maxAttempts) {
            const loadingIndicator = document.querySelector('.loading, .spinner, [class*="load"]');
            if (!loadingIndicator || loadingIndicator.style.display === 'none') {
                break;
            }
            await this.delay(1000);
            attempts++;
        }

        // 额外等待确保数据完全加载
        await this.delay(2000);
    }

    async collectTableData() {
        const data = [];
        const table = this.pageElements.dataTable;

        if (!table) return data;

        const headers = Array.from(table.querySelectorAll('th')).map(th => th.textContent.trim());
        const rows = table.querySelectorAll('tbody tr');

        rows.forEach((row, index) => {
            const cells = row.querySelectorAll('td');
            if (cells.length > 0) {
                const rowData = {
                    rowIndex: index,
                    timestamp: new Date().toISOString(),
                    data: {}
                };

                cells.forEach((cell, cellIndex) => {
                    const header = headers[cellIndex] || `column_${cellIndex}`;
                    rowData.data[header] = cell.textContent.trim();
                });

                data.push(rowData);
            }
        });

        return data;
    }

    detectAnomalies(data, task) {
        const anomalies = [];

        data.forEach(row => {
            const anomaly = {
                task: task.description,
                rowIndex: row.rowIndex,
                timestamp: row.timestamp,
                issues: []
            };

            // 检测空值
            if (this.config.detectNullValues) {
                Object.entries(row.data).forEach(([key, value]) => {
                    if (!value || value === '' || value === '-' || value === 'null') {
                        anomaly.issues.push({
                            type: 'null_value',
                            field: key,
                            value: value,
                            description: `字段 ${key} 为空值`
                        });
                    }
                });
            }

            // 检测数值异常
            if (this.config.thresholdValue) {
                Object.entries(row.data).forEach(([key, value]) => {
                    const numValue = parseFloat(value);
                    if (!isNaN(numValue) && numValue > this.config.thresholdValue) {
                        anomaly.issues.push({
                            type: 'threshold_exceeded',
                            field: key,
                            value: numValue,
                            threshold: this.config.thresholdValue,
                            description: `字段 ${key} 值 ${numValue} 超过阈值 ${this.config.thresholdValue}`
                        });
                    }
                });
            }

            // 检测超标数据
            if (this.config.detectExceedance) {
                Object.entries(row.data).forEach(([key, value]) => {
                    if (value && (value.includes('超标') || value.includes('异常') || value.includes('超限'))) {
                        anomaly.issues.push({
                            type: 'exceedance',
                            field: key,
                            value: value,
                            description: `字段 ${key} 检测到超标: ${value}`
                        });
                    }
                });
            }

            if (anomaly.issues.length > 0) {
                anomalies.push(anomaly);
            }
        });

        return anomalies;
    }

    async takeScreenshot(task, anomalies) {
        try {
            // 高亮异常数据
            this.highlightAnomalies(anomalies);

            // 使用Chrome API截图
            const dataUrl = await this.captureScreenshot();

            // 发送截图数据到background script保存
            chrome.runtime.sendMessage({
                action: 'saveScreenshot',
                data: dataUrl,
                filename: this.generateScreenshotFilename(task),
                anomalies: anomalies
            });

            // 移除高亮
            this.removeHighlights();

        } catch (error) {
            console.error('Screenshot failed:', error);
        }
    }

    highlightAnomalies(anomalies) {
        anomalies.forEach(anomaly => {
            const row = this.pageElements.dataTable?.querySelector(`tbody tr:nth-child(${anomaly.rowIndex + 1})`);
            if (row) {
                row.style.backgroundColor = '#ffebee';
                row.style.border = '2px solid #f44336';
                row.classList.add('anomaly-highlight');
            }
        });
    }

    removeHighlights() {
        const highlighted = document.querySelectorAll('.anomaly-highlight');
        highlighted.forEach(element => {
            element.style.backgroundColor = '';
            element.style.border = '';
            element.classList.remove('anomaly-highlight');
        });
    }

    async captureScreenshot() {
        return new Promise((resolve, reject) => {
            chrome.runtime.sendMessage({
                action: 'captureTab'
            }, (response) => {
                if (response && response.dataUrl) {
                    resolve(response.dataUrl);
                } else {
                    reject(new Error('截图失败'));
                }
            });
        });
    }

    generateScreenshotFilename(task) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const taskName = task.description.replace(/[^\w\s-]/g, '').replace(/\s+/g, '_');
        return `anomaly_${timestamp}_${taskName}.png`;
    }

    formatDateTime(date) {
        return date.toISOString().slice(0, 16);
    }

    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async waitForResume() {
        while (this.isPaused && this.isCollecting) {
            await this.delay(1000);
        }
    }

    stopCollection() {
        this.isCollecting = false;
        this.isPaused = false;
    }

    pauseCollection() {
        this.isPaused = !this.isPaused;
    }

    getProgress() {
        return {
            isCollecting: this.isCollecting,
            isPaused: this.isPaused,
            progress: this.progress,
            currentTask: this.currentTask,
            processedCount: this.processedCount,
            totalTasks: this.totalTasks,
            anomalyCount: this.anomalies.length,
            screenshotCount: this.anomalies.filter(a => a.screenshot).length
        };
    }

    getPageInfo() {
        return {
            isEnvironmentSystem: this.isEnvironmentSystem,
            url: window.location.href,
            title: document.title,
            elementCount: Object.keys(this.pageElements).length,
            hasTimeInputs: this.pageElements.timeInputs?.length > 0,
            hasDropdowns: this.pageElements.dropdowns?.length > 0,
            hasQueryButton: !!this.pageElements.queryButton,
            hasDataTable: !!this.pageElements.dataTable
        };
    }

    notifyProgress() {
        chrome.runtime.sendMessage({
            action: 'progressUpdate',
            progress: this.getProgress()
        });
    }

    notifyError(message) {
        chrome.runtime.sendMessage({
            action: 'error',
            message: message
        });
    }

    async generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            config: this.config,
            summary: {
                totalTasks: this.totalTasks,
                processedCount: this.processedCount,
                dataCollected: this.collectedData.length,
                anomaliesFound: this.anomalies.length
            },
            data: this.collectedData,
            anomalies: this.anomalies
        };

        chrome.runtime.sendMessage({
            action: 'saveReport',
            report: report
        });
    }
}

// 初始化采集器
const collector = new EnvironmentDataCollector();
