* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.6;
    color: #333;
    background: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    min-height: 100vh;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    text-align: center;
}

header h1 {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 8px;
}

header p {
    font-size: 16px;
    opacity: 0.9;
}

.tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 0 30px;
}

.tab-button {
    padding: 15px 25px;
    border: none;
    background: transparent;
    color: #666;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.tab-button:hover {
    color: #333;
    background: rgba(0,0,0,0.05);
}

.tab-button.active {
    color: #667eea;
    border-bottom-color: #667eea;
    background: white;
}

main {
    padding: 30px;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

h2 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 25px;
    color: #333;
    border-bottom: 2px solid #eee;
    padding-bottom: 10px;
}

.setting-group {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
}

.setting-group h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #495057;
}

.setting-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    gap: 15px;
}

.setting-item:last-child {
    margin-bottom: 0;
}

.setting-item label {
    min-width: 150px;
    font-weight: 500;
    color: #495057;
}

.setting-item input,
.setting-item select,
.setting-item textarea {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.setting-item input:focus,
.setting-item select:focus,
.setting-item textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.25);
}

.setting-item textarea {
    resize: vertical;
    min-height: 100px;
}

.setting-item small {
    color: #6c757d;
    font-size: 12px;
    margin-left: 10px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    min-width: auto;
}

.checkbox-label input[type="checkbox"] {
    width: 18px;
    height: 18px;
    flex: none;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

.btn-outline {
    background: transparent;
    color: #667eea;
    border: 1px solid #667eea;
}

.btn-outline:hover {
    background: #667eea;
    color: white;
    transform: translateY(-1px);
}

.btn-small {
    padding: 6px 12px;
    font-size: 12px;
}

.custom-rules {
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;
    background: white;
}

.custom-rule {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    margin-bottom: 10px;
    background: #f8f9fa;
}

.custom-rule input {
    flex: 1;
    margin: 0;
}

.custom-rule button {
    flex: none;
}

.log-controls {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    align-items: center;
}

.log-viewer {
    background: #1e1e1e;
    color: #d4d4d4;
    border-radius: 6px;
    padding: 20px;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 13px;
    line-height: 1.4;
    max-height: 500px;
    overflow-y: auto;
}

.log-entry {
    margin-bottom: 8px;
    padding: 5px 0;
    border-bottom: 1px solid #333;
}

.log-timestamp {
    color: #569cd6;
    margin-right: 10px;
}

.log-level {
    display: inline-block;
    width: 60px;
    text-align: center;
    margin-right: 10px;
    font-weight: bold;
}

.log-level.error {
    color: #f44747;
}

.log-level.warning {
    color: #ffcc02;
}

.log-level.info {
    color: #4ec9b0;
}

.log-level.debug {
    color: #9cdcfe;
}

.log-message {
    color: #d4d4d4;
}

.log-loading {
    text-align: center;
    color: #888;
    padding: 40px;
}

footer {
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
    padding: 25px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.actions {
    display: flex;
    gap: 15px;
}

.version-info {
    display: flex;
    align-items: center;
    gap: 15px;
    color: #6c757d;
    font-size: 13px;
}

.version-info a {
    color: #667eea;
    text-decoration: none;
}

.version-info a:hover {
    text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        margin: 0;
        box-shadow: none;
    }
    
    header {
        padding: 20px;
    }
    
    .tabs {
        flex-wrap: wrap;
        padding: 0 20px;
    }
    
    .tab-button {
        padding: 12px 15px;
        font-size: 13px;
    }
    
    main {
        padding: 20px;
    }
    
    .setting-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .setting-item label {
        min-width: auto;
    }
    
    .setting-item input,
    .setting-item select,
    .setting-item textarea {
        width: 100%;
    }
    
    footer {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
    
    .actions {
        flex-wrap: wrap;
        justify-content: center;
    }
}

/* 滚动条样式 */
.log-viewer::-webkit-scrollbar {
    width: 8px;
}

.log-viewer::-webkit-scrollbar-track {
    background: #2d2d30;
}

.log-viewer::-webkit-scrollbar-thumb {
    background: #464647;
    border-radius: 4px;
}

.log-viewer::-webkit-scrollbar-thumb:hover {
    background: #5a5a5c;
}
