# 环境监测数据采集器 - 页面分析工具

## 当前问题分析

根据日志显示：
- ✅ 找到查询按钮: "查询分析"
- ✅ 找到1个表格
- ❌ 找到0个下拉框
- ❌ 未找到数据表格（表格不符合数据表格的特征）

## 问题原因

1. **下拉框识别失败**：页面使用了自定义下拉控件，不是标准的`<select>`元素
2. **数据表格识别失败**：表格可能是布局表格，不是数据表格
3. **CSP错误**：页面的内容安全策略阻止了某些JavaScript操作

## 解决方案

### 1. 手动页面分析

在浏览器控制台中运行以下代码来分析页面结构：

```javascript
// 分析所有可能的下拉框
console.log('=== 页面下拉框分析 ===');

// 1. 标准select元素
const selects = document.querySelectorAll('select');
console.log(`标准select元素: ${selects.length}个`);
selects.forEach((select, i) => {
    console.log(`Select ${i+1}:`, {
        id: select.id,
        name: select.name,
        className: select.className,
        options: select.options.length
    });
});

// 2. 自定义下拉框
const customDropdowns = document.querySelectorAll(`
    .dropdown, .select, .combobox, .picker,
    [class*="dropdown"], [class*="select"], [class*="combo"]
`);
console.log(`自定义下拉框: ${customDropdowns.length}个`);
customDropdowns.forEach((elem, i) => {
    console.log(`Custom ${i+1}:`, {
        tagName: elem.tagName,
        id: elem.id,
        className: elem.className,
        text: elem.textContent?.trim().substring(0, 50)
    });
});

// 3. 包含特定文本的元素
const textElements = Array.from(document.querySelectorAll('*')).filter(elem => {
    const text = elem.textContent?.trim() || '';
    return text.includes('排口') || text.includes('区域') || text.includes('企业') || 
           text.includes('监测') || text.includes('项目') || text.includes('请选择');
});
console.log(`包含关键词的元素: ${textElements.length}个`);
textElements.slice(0, 10).forEach((elem, i) => {
    console.log(`Text ${i+1}:`, {
        tagName: elem.tagName,
        className: elem.className,
        text: elem.textContent?.trim().substring(0, 50)
    });
});
```

### 2. 表格分析

```javascript
// 分析所有表格
console.log('=== 页面表格分析 ===');
const tables = document.querySelectorAll('table');
console.log(`找到 ${tables.length} 个表格`);

tables.forEach((table, i) => {
    const headers = table.querySelectorAll('th');
    const rows = table.querySelectorAll('tr');
    const cells = table.querySelectorAll('td');
    
    console.log(`表格 ${i+1}:`, {
        headers: headers.length,
        rows: rows.length,
        cells: cells.length,
        className: table.className,
        id: table.id
    });
    
    // 显示表头内容
    if (headers.length > 0) {
        const headerTexts = Array.from(headers).map(th => th.textContent?.trim());
        console.log(`表头: ${headerTexts.join(' | ')}`);
    }
});

// 查找其他可能的数据容器
const dataContainers = document.querySelectorAll(`
    .data-table, .grid, .datagrid, [class*="table"],
    .list, .data-list, [class*="list"]
`);
console.log(`数据容器: ${dataContainers.length}个`);
```

### 3. 查询按钮分析

```javascript
// 分析查询按钮
console.log('=== 查询按钮分析 ===');
const buttons = document.querySelectorAll('button, input[type="button"], input[type="submit"], a');
const queryButtons = Array.from(buttons).filter(btn => {
    const text = btn.textContent || btn.value || '';
    return text.includes('查询') || text.includes('搜索') || text.includes('检索');
});

console.log(`查询按钮: ${queryButtons.length}个`);
queryButtons.forEach((btn, i) => {
    console.log(`按钮 ${i+1}:`, {
        tagName: btn.tagName,
        type: btn.type,
        className: btn.className,
        id: btn.id,
        text: (btn.textContent || btn.value || '').trim(),
        href: btn.href
    });
});
```

## 使用步骤

### 第一步：页面分析
1. 打开环境监测系统页面
2. 按F12打开开发者工具
3. 切换到Console标签
4. 复制上面的分析代码并执行
5. 查看输出结果，了解页面结构

### 第二步：手动测试
根据分析结果，手动测试页面功能：
1. 尝试点击可能的下拉框元素
2. 查看是否有选项列表出现
3. 尝试选择不同的选项
4. 点击查询按钮查看效果

### 第三步：配置插件
基于分析结果：
1. 如果找到了下拉框，插件应该能识别
2. 如果没有找到，说明页面使用了特殊控件
3. 可以只启用基础查询功能

## 常见页面类型及解决方案

### 1. 使用LayUI框架
```javascript
// LayUI下拉框特征
.layui-select, .layui-form-select
```

### 2. 使用Element UI
```javascript
// Element UI下拉框特征
.el-select, .el-select-dropdown
```

### 3. 使用Ant Design
```javascript
// Ant Design下拉框特征
.ant-select, .ant-select-dropdown
```

### 4. 自定义实现
```javascript
// 常见自定义下拉框特征
.dropdown, .select-box, .combo-box
```

## 临时解决方案

如果插件无法识别页面元素，可以：

### 1. 手动操作模式
1. 手动设置所有筛选条件
2. 只使用插件的数据收集和异常检测功能
3. 插件执行基础查询任务

### 2. 简化配置
1. 取消所有自动遍历选项
2. 只启用异常检测
3. 手动切换条件后让插件分析当前数据

### 3. 分步执行
1. 手动设置第一组条件
2. 启动插件收集数据
3. 手动切换到下一组条件
4. 重复执行

## 调试技巧

### 1. 元素高亮
```javascript
// 高亮显示找到的元素
function highlightElement(selector, color = 'red') {
    document.querySelectorAll(selector).forEach(elem => {
        elem.style.border = `2px solid ${color}`;
        elem.style.backgroundColor = `${color}20`;
    });
}

// 使用示例
highlightElement('select', 'blue');
highlightElement('.dropdown', 'green');
```

### 2. 事件监听
```javascript
// 监听页面上的点击事件
document.addEventListener('click', function(e) {
    console.log('点击元素:', {
        tagName: e.target.tagName,
        className: e.target.className,
        id: e.target.id,
        text: e.target.textContent?.trim().substring(0, 50)
    });
}, true);
```

### 3. 动态观察
```javascript
// 观察DOM变化
const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
        if (mutation.type === 'childList') {
            console.log('DOM发生变化:', mutation);
        }
    });
});

observer.observe(document.body, {
    childList: true,
    subtree: true
});
```

通过这些分析工具，您可以更好地了解页面结构，帮助插件正确识别和操作页面元素。
