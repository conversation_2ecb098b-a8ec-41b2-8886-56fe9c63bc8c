# 环境监测数据自动采集器 - 安装指南

## 快速开始

### 第一步：准备图标文件（可选）
1. 在 `icons` 文件夹中放置以下图标文件：
   - icon16.png (16x16)
   - icon32.png (32x32) 
   - icon48.png (48x48)
   - icon128.png (128x128)

   如果没有图标文件，插件仍可正常工作，只是会显示默认图标。

### 第二步：安装插件

#### 方法一：开发者模式安装（推荐）
1. 打开Chrome浏览器
2. 在地址栏输入 `chrome://extensions/` 并回车
3. 开启右上角的"开发者模式"开关
4. 点击"加载已解压的扩展程序"按钮
5. 选择本项目的根目录文件夹
6. 点击"选择文件夹"完成安装

#### 方法二：打包后安装
1. 在扩展程序页面点击"打包扩展程序"
2. 选择本项目根目录作为"扩展程序根目录"
3. 点击"打包扩展程序"生成.crx文件
4. 将生成的.crx文件拖拽到扩展程序页面进行安装

### 第三步：验证安装
1. 安装成功后，浏览器工具栏会出现插件图标
2. 点击图标应该能打开插件控制面板
3. 在扩展程序页面可以看到"环境监测数据自动采集器"

## 使用教程

### 基础使用

1. **访问环境监测系统**
   ```
   打开您要监控的环境监测系统网页
   插件会自动识别页面类型
   如果识别成功，插件图标会显示绿色✓标记
   ```

2. **配置采集参数**
   ```
   点击插件图标打开控制面板

   选择时间配置模式：
   ○ 使用页面中的时间控件（推荐）
     - 插件会自动操作页面中的小时选择、开始时间、结束时间
     - 自动遍历页面中的所有时间选项
   ○ 自定义时间范围
     - 手动设置开始时间和结束时间
     - 选择查询间隔（1-24小时）

   勾选需要遍历的选项：
   ✓ 自动遍历所有排口类型
   ✓ 自动遍历所有区域
   ✓ 自动遍历所有企业
   ✓ 自动遍历所有监测项目
   ```

3. **设置异常检测**
   ```
   数值异常阈值：输入数值，超过此值将标记为异常
   ✓ 标记空值或无效数据
   ✓ 自动检测超标数据
   ```

4. **配置保存设置**
   ```
   点击"选择文件夹"设置截图保存路径
   ✓ 发现异常时自动截图
   ```

5. **开始采集**
   ```
   点击"开始采集"按钮
   实时查看采集进度
   监控发现的异常数据数量
   ```

### 高级功能

#### 自定义检测规则
1. 点击"高级设置"打开选项页面
2. 切换到"检测规则"标签
3. 点击"添加自定义规则"
4. 配置规则：
   - 字段名：要检测的数据列名
   - 操作符：选择比较方式（大于、小于、等于、包含等）
   - 值：设置比较值

#### 批量数据导出
1. 采集完成后点击"导出报告"
2. 选择导出格式（JSON/CSV/Excel）
3. 报告包含：
   - 采集统计信息
   - 所有收集的数据
   - 异常数据列表
   - 截图文件信息

#### 日志查看
1. 打开"高级设置" → "日志管理"
2. 查看详细的采集日志
3. 可按日志级别筛选
4. 支持导出日志文件

## 常见问题解决

### 问题1：插件无法识别环境监测系统
**症状**：插件图标没有显示绿色✓，状态显示"未识别为环境监测系统"

**解决方案**：
1. 确保页面包含以下关键词之一：
   - 排口类型、所属区域、企业名称、排口名称、监测项目
   - 实时数据、历史数据、环境监测、污染源
2. 等待页面完全加载后再尝试
3. 刷新页面重新检测

### 问题2：采集过程中出现错误
**症状**：采集中断，显示错误信息

**解决方案**：
1. 检查网络连接是否稳定
2. 确认页面没有弹出验证码或登录框
3. 在高级设置中启用"遇到错误时自动重试"
4. 适当增加"查询延迟"时间

### 问题3：截图保存失败
**症状**：发现异常但没有生成截图文件

**解决方案**：
1. 确认已正确设置保存路径
2. 检查文件夹是否存在且有写入权限
3. 确保磁盘空间充足
4. 尝试更换保存路径

### 问题4：采集速度太慢
**症状**：采集进度很慢，效率低下

**解决方案**：
1. 在高级设置中减少"查询延迟"时间
2. 适当增加"并发查询数"（建议不超过3）
3. 关闭不必要的检测规则
4. 缩小时间范围或增大查询间隔

### 问题5：内存占用过高
**症状**：浏览器变慢，内存使用率高

**解决方案**：
1. 减少并发查询数
2. 启用"自动清理过期数据"
3. 定期清空日志
4. 分批次进行大量数据采集

## 性能优化建议

### 采集效率优化
```
推荐设置：
- 查询间隔：根据数据更新频率设置，一般2-4小时
- 查询延迟：1000-2000毫秒
- 并发查询：1-2个
- 页面超时：30秒
```

### 存储空间优化
```
建议配置：
- 数据保留天数：30天
- 启用自动清理
- 截图质量：中等质量
- 定期导出并清理历史数据
```

### 系统资源优化
```
最佳实践：
- 关闭不必要的浏览器标签页
- 定期重启浏览器
- 监控系统内存使用情况
- 避免同时运行多个采集任务
```

## 技术支持

### 获取帮助
- 查看详细文档：README.md
- 提交问题：GitHub Issues
- 邮件联系：<EMAIL>

### 调试模式
如果遇到问题，可以启用调试模式：
1. 打开"高级设置" → "高级选项"
2. 勾选"启用调试模式"和"详细日志记录"
3. 重现问题并查看详细日志
4. 将日志信息提供给技术支持

### 版本更新
- 定期检查GitHub仓库获取最新版本
- 更新时请先导出重要数据和配置
- 新版本安装后重新导入配置

---

如有其他问题，请参考完整文档或联系技术支持。
