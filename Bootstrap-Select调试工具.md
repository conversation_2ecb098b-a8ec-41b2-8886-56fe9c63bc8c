# Bootstrap Select 调试工具

## 问题分析

根据日志显示，插件成功启动但只创建了基础查询任务，说明下拉框识别可能有问题。让我们用调试工具来检查页面状态。

## 调试步骤

### 第一步：检查页面加载状态

在浏览器控制台中执行：

```javascript
// 检查页面基本状态
console.log('=== 页面基本状态检查 ===');
console.log('jQuery加载:', typeof window.$);
console.log('Bootstrap Select加载:', typeof window.$.fn.selectpicker);
console.log('HistoryReport对象:', typeof window.HistoryReport);
console.log('页面标题:', document.title);
```

### 第二步：检查Bootstrap Select元素

```javascript
// 检查Bootstrap Select下拉框
console.log('=== Bootstrap Select 下拉框检查 ===');

const selectIds = ['sel_SubType', 'sel_City', 'sel_ent', 'select_sub', 'sel_Item'];

selectIds.forEach(id => {
    const select = document.getElementById(id);
    console.log(`\n${id}:`, {
        存在: !!select,
        可见: select ? select.offsetParent !== null : false,
        值: select?.value,
        选项数: select?.options?.length,
        Bootstrap初始化: select ? !!$(select).data('selectpicker') : false,
        类名: select?.className,
        父容器: select?.parentElement?.className
    });
    
    if (select && select.options) {
        console.log(`${id} 选项列表:`, Array.from(select.options).slice(0, 5).map(opt => ({
            value: opt.value,
            text: opt.text
        })));
    }
});
```

### 第三步：检查Bootstrap Select容器

```javascript
// 检查Bootstrap Select生成的容器
console.log('=== Bootstrap Select 容器检查 ===');

const bootstrapContainers = document.querySelectorAll('.bootstrap-select');
console.log(`找到 ${bootstrapContainers.length} 个 Bootstrap Select 容器`);

bootstrapContainers.forEach((container, index) => {
    const button = container.querySelector('.dropdown-toggle');
    const menu = container.querySelector('.dropdown-menu');
    const originalSelect = container.querySelector('select');
    
    console.log(`容器 ${index + 1}:`, {
        容器类名: container.className,
        按钮存在: !!button,
        菜单存在: !!menu,
        原始select: originalSelect?.id,
        按钮文本: button?.textContent?.trim(),
        菜单选项数: menu?.querySelectorAll('li').length
    });
});
```

### 第四步：手动测试Bootstrap Select

```javascript
// 手动测试Bootstrap Select功能
console.log('=== 手动测试 Bootstrap Select ===');

function testBootstrapSelect(selectId) {
    const select = document.getElementById(selectId);
    if (!select) {
        console.log(`${selectId}: 元素不存在`);
        return;
    }
    
    console.log(`\n测试 ${selectId}:`);
    
    // 获取当前值
    console.log('当前值:', select.value);
    
    // 获取所有选项
    const options = Array.from(select.options).filter(opt => opt.value && opt.value !== '');
    console.log('有效选项数:', options.length);
    
    if (options.length > 0) {
        // 尝试设置第一个选项
        const firstOption = options[0];
        console.log('尝试设置为:', firstOption.text);
        
        try {
            // 方法1: 直接设置
            select.value = firstOption.value;
            
            // 方法2: Bootstrap Select API
            if (window.$ && window.$.fn.selectpicker) {
                $(select).selectpicker('val', firstOption.value);
                $(select).selectpicker('refresh');
            }
            
            // 触发change事件
            select.dispatchEvent(new Event('change', { bubbles: true }));
            
            console.log('设置后的值:', select.value);
            console.log('设置成功');
        } catch (error) {
            console.log('设置失败:', error.message);
        }
    }
}

// 测试所有下拉框
['sel_SubType', 'sel_City', 'sel_ent', 'select_sub', 'sel_Item'].forEach(testBootstrapSelect);
```

### 第五步：检查插件识别结果

```javascript
// 检查插件的识别结果
console.log('=== 插件识别结果检查 ===');

// 检查全局collector对象
if (window.collector) {
    console.log('插件collector对象存在');
    console.log('页面元素:', window.collector.pageElements);
    console.log('下拉框数量:', window.collector.pageElements?.dropdowns?.length || 0);
    
    if (window.collector.pageElements?.dropdowns) {
        window.collector.pageElements.dropdowns.forEach((dropdown, index) => {
            console.log(`插件识别的下拉框 ${index + 1}:`, {
                ID: dropdown.id,
                类型: dropdown.type,
                标签: dropdown.label,
                选项数: dropdown.options?.length || 0,
                元素类型: dropdown.elementType
            });
        });
    }
} else {
    console.log('插件collector对象不存在');
}
```

## 常见问题及解决方案

### 问题1：Bootstrap Select未初始化
**症状**：`Bootstrap初始化: false`
**解决方案**：
```javascript
// 手动初始化Bootstrap Select
$('.selectpicker').selectpicker();
```

### 问题2：选项为空
**症状**：`选项数: 0` 或 `选项数: 1`（只有默认选项）
**可能原因**：
- 页面数据还在加载中
- 需要先设置其他下拉框触发级联加载
- 权限问题导致数据无法加载

**解决方案**：
1. 等待页面完全加载
2. 手动操作一下下拉框，触发数据加载
3. 检查网络请求是否成功

### 问题3：下拉框不可见
**症状**：`可见: false`
**解决方案**：
```javascript
// 检查是否被隐藏
const select = document.getElementById('sel_SubType');
console.log('display:', getComputedStyle(select).display);
console.log('visibility:', getComputedStyle(select).visibility);
```

### 问题4：插件无法识别
**症状**：插件识别的下拉框数量为0
**解决方案**：
1. 确保页面完全加载后再启动插件
2. 检查控制台是否有JavaScript错误
3. 尝试刷新页面重新识别

## 修复建议

根据调试结果，可能需要：

### 1. 等待页面完全加载
```javascript
// 在插件中添加等待逻辑
async function waitForBootstrapSelect() {
    let attempts = 0;
    while (attempts < 10) {
        const selects = document.querySelectorAll('.selectpicker');
        const initialized = Array.from(selects).every(select => 
            $(select).data('selectpicker')
        );
        
        if (initialized) break;
        
        await new Promise(resolve => setTimeout(resolve, 1000));
        attempts++;
    }
}
```

### 2. 手动触发数据加载
```javascript
// 触发级联下拉框的数据加载
if (window.HistoryReport && window.HistoryReport.Init) {
    window.HistoryReport.Init();
}
```

### 3. 重新分析页面
```javascript
// 在插件中重新分析页面结构
if (window.collector) {
    window.collector.analyzePageStructure();
}
```

## 使用方法

1. 打开环境监测系统页面
2. 等待页面完全加载（看到所有下拉框都正常显示）
3. 打开开发者工具（F12 → Console）
4. 依次执行上面的调试代码
5. 根据输出结果判断问题所在
6. 应用相应的修复方案
7. 重新测试插件功能

通过这个调试工具，我们可以准确定位Bootstrap Select的问题并进行针对性修复。
