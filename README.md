# 环境监测数据自动采集器

一个专门用于环境监测系统的浏览器插件，能够自动遍历查询环境监测数据，检测异常数据并自动截图保存。

## 功能特性

### 🔍 智能识别
- 自动识别环境监测系统页面
- 智能分析页面结构，识别关键元素
- 支持多种环境监测系统界面

### ⏰ 时间配置
- 灵活的时间范围设置（开始时间、结束时间）
- 可配置的查询间隔（1小时到24小时）
- 支持跨天、跨月的长时间数据采集

### 🔄 自动遍历
- **排口类型**：自动遍历所有排口类型
- **所属区域**：自动遍历所有监测区域
- **企业名称**：自动遍历所有企业
- **排口名称**：自动遍历所有排口
- **监测项目**：自动遍历所有监测项目

### 🚨 异常检测
- **数值异常**：设置阈值，自动检测超标数据
- **空值检测**：识别空值、无效数据
- **关键词检测**：检测"超标"、"异常"、"故障"等关键词
- **自定义规则**：支持用户自定义检测规则

### 📸 自动截图
- 发现异常数据时自动截图
- 高亮显示异常数据行
- 可配置截图质量和格式
- 自动保存到指定文件夹

### 📊 数据管理
- 完整的数据收集和存储
- 支持JSON、CSV、Excel格式导出
- 详细的采集报告生成
- 数据去重和清理功能

## 安装方法

### 方法一：开发者模式安装
1. 打开Chrome浏览器，进入 `chrome://extensions/`
2. 开启右上角的"开发者模式"
3. 点击"加载已解压的扩展程序"
4. 选择本项目文件夹
5. 插件安装完成

### 方法二：打包安装
1. 在扩展程序页面点击"打包扩展程序"
2. 选择本项目文件夹，生成.crx文件
3. 将.crx文件拖拽到扩展程序页面安装

## 使用指南

### 基本使用流程

1. **打开环境监测系统页面**
   - 插件会自动识别环境监测系统
   - 插件图标显示绿色✓表示识别成功

2. **配置采集参数**
   - 点击插件图标打开控制面板
   - 设置开始时间和结束时间
   - 选择查询间隔
   - 配置遍历选项

3. **设置异常检测规则**
   - 设置数值异常阈值
   - 启用空值检测
   - 配置关键词检测
   - 添加自定义规则

4. **选择保存路径**
   - 设置截图保存文件夹
   - 启用自动截图功能

5. **开始数据采集**
   - 点击"开始采集"按钮
   - 实时查看采集进度
   - 监控异常数据发现情况

### 高级功能

#### 自定义检测规则
在选项页面可以添加自定义检测规则：
- 字段名：指定要检测的数据字段
- 操作符：支持 >、<、=、!=、包含、正则表达式
- 值：设置比较值或匹配模式

#### 批量数据处理
- 支持并发查询（可配置并发数）
- 智能重试机制
- 自动分页处理
- 数据去重和合并

#### 报告生成
采集完成后自动生成详细报告：
- 采集统计信息
- 异常数据汇总
- 截图文件列表
- 时间线分析

## 配置选项

### 常规设置
- 默认时间范围
- 默认查询间隔
- 自动启动选项
- 重试配置

### 检测规则
- 阈值检测设置
- 范围检测配置
- 关键词列表
- 自定义规则

### 存储设置
- 截图保存路径
- 截图质量和格式
- 数据导出格式
- 自动清理配置

### 高级选项
- 性能参数调优
- 调试模式
- 系统集成选项

## 技术架构

### 核心组件
- **Content Script** (`content.js`)：页面内容分析和数据采集
- **Background Script** (`background.js`)：后台任务处理和数据管理
- **Popup** (`popup.html/js`)：用户界面和控制面板
- **Options** (`options.html/js`)：高级设置和配置管理

### 数据流程
1. 页面识别 → 元素分析 → 采集计划生成
2. 参数设置 → 查询执行 → 数据收集
3. 异常检测 → 截图保存 → 报告生成

### 存储机制
- 使用Chrome Storage API进行本地存储
- 支持大容量数据存储
- 自动数据备份和恢复

## 兼容性

### 支持的浏览器
- Chrome 88+
- Edge 88+
- 其他基于Chromium的浏览器

### 支持的系统
- Windows 10/11
- macOS 10.14+
- Linux (Ubuntu 18.04+)

## 常见问题

### Q: 插件无法识别环境监测系统？
A: 请确保页面包含"排口类型"、"所属区域"、"企业名称"等关键词，插件会根据这些特征进行识别。

### Q: 采集过程中出现错误怎么办？
A: 插件内置重试机制，可在高级设置中调整重试次数和间隔。如持续出错，请检查网络连接和页面状态。

### Q: 截图保存失败？
A: 请确保：
1. 已正确设置保存路径
2. 文件夹具有写入权限
3. 磁盘空间充足

### Q: 如何提高采集效率？
A: 可以在高级设置中：
1. 适当增加并发查询数
2. 减少查询延迟时间
3. 关闭不必要的检测规则

## 更新日志

### v1.0.0 (2024-07-30)
- 初始版本发布
- 支持基本的数据采集功能
- 实现异常检测和自动截图
- 提供完整的配置选项

## 开发计划

### 近期计划
- [ ] 支持更多环境监测系统
- [ ] 增加数据可视化功能
- [ ] 优化性能和稳定性
- [ ] 添加数据分析工具

### 长期计划
- [ ] 支持定时任务
- [ ] 云端数据同步
- [ ] 移动端支持
- [ ] API接口开放

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目。

### 开发环境设置
1. 克隆项目到本地
2. 在Chrome中加载扩展程序
3. 修改代码后重新加载扩展

### 代码规范
- 使用ES6+语法
- 遵循Google JavaScript风格指南
- 添加必要的注释和文档

## 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 联系方式

- 项目主页：[GitHub Repository]
- 问题反馈：[GitHub Issues]
- 邮箱：<EMAIL>

---

**注意**：本插件仅用于合法的环境监测数据采集，请遵守相关法律法规和网站使用条款。
