* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.4;
    color: #333;
    background: #f5f5f5;
}

.container {
    width: 400px;
    max-height: 600px;
    overflow-y: auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px;
    border-radius: 8px 8px 0 0;
}

header h1 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
}

.status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #ffd700;
}

.status-indicator.active {
    background: #4CAF50;
}

.status-indicator.error {
    background: #f44336;
}

main {
    padding: 16px;
}

h3 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #555;
    border-bottom: 1px solid #eee;
    padding-bottom: 4px;
}

.time-config, .query-config, .detection-rules, .save-config {
    margin-bottom: 20px;
    padding: 12px;
    background: #f9f9f9;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
}

.time-inputs {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.input-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.input-group label {
    min-width: 70px;
    font-size: 12px;
    color: #666;
}

.input-group input, .input-group select {
    flex: 1;
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 12px;
}

.config-options {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    width: 16px;
    height: 16px;
}

.rule-item {
    margin-bottom: 12px;
}

.rule-item label {
    display: block;
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
}

.rule-item input {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 12px;
}

.control-panel {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    flex: 1;
}

.btn-primary {
    background: #4CAF50;
    color: white;
}

.btn-primary:hover {
    background: #45a049;
}

.btn-secondary {
    background: #f44336;
    color: white;
}

.btn-secondary:hover {
    background: #da190b;
}

.btn-outline {
    background: transparent;
    color: #666;
    border: 1px solid #ddd;
}

.btn-outline:hover {
    background: #f5f5f5;
}

.btn-small {
    padding: 4px 8px;
    font-size: 11px;
    min-width: auto;
    flex: none;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.progress-section {
    background: #f0f8ff;
    border: 1px solid #b3d9ff;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 16px;
}

.progress-bar {
    position: relative;
    height: 20px;
    background: #e0e0e0;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 12px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #45a049);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 11px;
    font-weight: 600;
    color: #333;
}

.progress-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    font-size: 11px;
}

.detail-item span:first-child {
    color: #666;
}

.detail-item span:last-child {
    font-weight: 600;
    color: #333;
}

.actions {
    display: flex;
    gap: 8px;
    margin-top: 16px;
}

/* 滚动条样式 */
.container::-webkit-scrollbar {
    width: 6px;
}

.container::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 响应式调整 */
@media (max-height: 500px) {
    .container {
        max-height: 450px;
    }
    
    .time-config, .query-config, .detection-rules, .save-config {
        margin-bottom: 12px;
        padding: 8px;
    }
    
    main {
        padding: 12px;
    }
}
