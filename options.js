// 环境监测数据采集器 - Options Script
class OptionsManager {
    constructor() {
        this.defaultSettings = {
            // 常规设置
            defaultTimeRange: 24,
            defaultInterval: 1,
            autoStart: false,
            autoRetry: true,
            retryCount: 3,
            retryDelay: 5,
            
            // 检测规则
            enableThresholdDetection: true,
            globalThreshold: null,
            enableRangeDetection: false,
            rangeMin: null,
            rangeMax: null,
            enableKeywordDetection: true,
            anomalyKeywords: '超标\n异常\n故障\n离线\n无数据',
            caseSensitive: false,
            
            // 存储设置
            defaultSavePath: '',
            screenshotQuality: 0.8,
            screenshotFormat: 'png',
            exportFormat: 'json',
            includeScreenshots: true,
            autoExport: false,
            dataRetentionDays: 30,
            autoCleanup: true,
            
            // 高级选项
            concurrentQueries: 1,
            queryDelay: 1000,
            pageTimeout: 30,
            enableDebugMode: false,
            verboseLogging: false,
            saveDebugInfo: false,
            enableNotifications: true,
            enableContextMenu: true
        };
        
        this.customRules = [];
        this.init();
    }

    async init() {
        // 初始化标签页切换
        this.initTabs();
        
        // 加载设置
        await this.loadSettings();
        
        // 绑定事件
        this.bindEvents();
        
        // 加载日志
        await this.loadLogs();
    }

    initTabs() {
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const tabId = button.dataset.tab;
                
                // 移除所有活动状态
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));
                
                // 激活当前标签
                button.classList.add('active');
                document.getElementById(tabId).classList.add('active');
            });
        });
    }

    async loadSettings() {
        try {
            const result = await chrome.storage.local.get('advancedSettings');
            const settings = { ...this.defaultSettings, ...result.advancedSettings };
            
            // 应用设置到界面
            Object.keys(settings).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    if (element.type === 'checkbox') {
                        element.checked = settings[key];
                    } else {
                        element.value = settings[key] || '';
                    }
                }
            });
            
            // 加载自定义规则
            const customRulesResult = await chrome.storage.local.get('customRules');
            this.customRules = customRulesResult.customRules || [];
            this.renderCustomRules();
            
        } catch (error) {
            console.error('Failed to load settings:', error);
        }
    }

    async saveSettings() {
        try {
            const settings = {};
            
            // 收集所有设置
            Object.keys(this.defaultSettings).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    if (element.type === 'checkbox') {
                        settings[key] = element.checked;
                    } else if (element.type === 'number') {
                        settings[key] = parseFloat(element.value) || this.defaultSettings[key];
                    } else {
                        settings[key] = element.value || this.defaultSettings[key];
                    }
                }
            });
            
            // 保存设置
            await chrome.storage.local.set({ 
                advancedSettings: settings,
                customRules: this.customRules
            });
            
            // 显示保存成功消息
            this.showMessage('设置已保存', 'success');
            
        } catch (error) {
            console.error('Failed to save settings:', error);
            this.showMessage('保存失败: ' + error.message, 'error');
        }
    }

    async resetSettings() {
        if (confirm('确定要重置所有设置为默认值吗？此操作不可撤销。')) {
            try {
                // 清除存储的设置
                await chrome.storage.local.remove(['advancedSettings', 'customRules']);
                
                // 重新加载默认设置
                this.customRules = [];
                await this.loadSettings();
                
                this.showMessage('设置已重置为默认值', 'success');
                
            } catch (error) {
                console.error('Failed to reset settings:', error);
                this.showMessage('重置失败: ' + error.message, 'error');
            }
        }
    }

    bindEvents() {
        // 保存设置
        document.getElementById('saveSettings').addEventListener('click', () => {
            this.saveSettings();
        });
        
        // 重置设置
        document.getElementById('resetSettings').addEventListener('click', () => {
            this.resetSettings();
        });
        
        // 导入/导出配置
        document.getElementById('importSettings').addEventListener('click', () => {
            this.importSettings();
        });
        
        document.getElementById('exportSettings').addEventListener('click', () => {
            this.exportSettings();
        });
        
        // 选择默认路径
        document.getElementById('selectDefaultPath').addEventListener('click', () => {
            this.selectDefaultPath();
        });
        
        // 添加自定义规则
        document.getElementById('addCustomRule').addEventListener('click', () => {
            this.addCustomRule();
        });
        
        // 数据清理
        document.getElementById('cleanupNow').addEventListener('click', () => {
            this.cleanupData();
        });
        
        document.getElementById('exportAll').addEventListener('click', () => {
            this.exportAllData();
        });
        
        // 日志管理
        document.getElementById('refreshLogs').addEventListener('click', () => {
            this.loadLogs();
        });
        
        document.getElementById('clearLogs').addEventListener('click', () => {
            this.clearLogs();
        });
        
        document.getElementById('exportLogs').addEventListener('click', () => {
            this.exportLogs();
        });
        
        document.getElementById('logLevel').addEventListener('change', () => {
            this.filterLogs();
        });
        
        // 帮助链接
        document.getElementById('helpLink').addEventListener('click', (e) => {
            e.preventDefault();
            this.openHelp();
        });
        
        document.getElementById('feedbackLink').addEventListener('click', (e) => {
            e.preventDefault();
            this.openFeedback();
        });
    }

    renderCustomRules() {
        const container = document.getElementById('customRules');
        container.innerHTML = '';
        
        if (this.customRules.length === 0) {
            container.innerHTML = '<p style="color: #666; text-align: center; padding: 20px;">暂无自定义规则</p>';
            return;
        }
        
        this.customRules.forEach((rule, index) => {
            const ruleElement = document.createElement('div');
            ruleElement.className = 'custom-rule';
            ruleElement.innerHTML = `
                <input type="text" placeholder="字段名" value="${rule.field || ''}" data-field="field" data-index="${index}">
                <select data-field="operator" data-index="${index}">
                    <option value=">" ${rule.operator === '>' ? 'selected' : ''}>大于</option>
                    <option value="<" ${rule.operator === '<' ? 'selected' : ''}>小于</option>
                    <option value="=" ${rule.operator === '=' ? 'selected' : ''}>等于</option>
                    <option value="!=" ${rule.operator === '!=' ? 'selected' : ''}>不等于</option>
                    <option value="contains" ${rule.operator === 'contains' ? 'selected' : ''}>包含</option>
                    <option value="regex" ${rule.operator === 'regex' ? 'selected' : ''}>正则表达式</option>
                </select>
                <input type="text" placeholder="值" value="${rule.value || ''}" data-field="value" data-index="${index}">
                <button type="button" class="btn btn-small btn-secondary" onclick="optionsManager.removeCustomRule(${index})">删除</button>
            `;
            container.appendChild(ruleElement);
        });
        
        // 绑定输入事件
        container.querySelectorAll('input, select').forEach(element => {
            element.addEventListener('change', (e) => {
                const index = parseInt(e.target.dataset.index);
                const field = e.target.dataset.field;
                this.customRules[index][field] = e.target.value;
            });
        });
    }

    addCustomRule() {
        this.customRules.push({
            field: '',
            operator: '>',
            value: ''
        });
        this.renderCustomRules();
    }

    removeCustomRule(index) {
        this.customRules.splice(index, 1);
        this.renderCustomRules();
    }

    async loadLogs() {
        try {
            const response = await chrome.runtime.sendMessage({ action: 'getLogs' });
            if (response && response.logs) {
                this.renderLogs(response.logs);
            }
        } catch (error) {
            console.error('Failed to load logs:', error);
        }
    }

    renderLogs(logs) {
        const viewer = document.getElementById('logViewer');
        const level = document.getElementById('logLevel').value;
        
        const filteredLogs = level === 'all' ? logs : logs.filter(log => log.level === level);
        
        if (filteredLogs.length === 0) {
            viewer.innerHTML = '<div class="log-loading">暂无日志记录</div>';
            return;
        }
        
        viewer.innerHTML = filteredLogs.map(log => `
            <div class="log-entry">
                <span class="log-timestamp">${new Date(log.timestamp).toLocaleString()}</span>
                <span class="log-level ${log.level}">${log.level.toUpperCase()}</span>
                <span class="log-message">${log.message}</span>
                ${log.data ? `<pre style="margin-top: 5px; color: #9cdcfe;">${JSON.stringify(log.data, null, 2)}</pre>` : ''}
            </div>
        `).join('');
        
        // 滚动到底部
        viewer.scrollTop = viewer.scrollHeight;
    }

    filterLogs() {
        this.loadLogs();
    }

    async clearLogs() {
        if (confirm('确定要清空所有日志吗？')) {
            try {
                await chrome.runtime.sendMessage({ action: 'clearLogs' });
                this.loadLogs();
                this.showMessage('日志已清空', 'success');
            } catch (error) {
                console.error('Failed to clear logs:', error);
                this.showMessage('清空失败: ' + error.message, 'error');
            }
        }
    }

    async exportLogs() {
        try {
            const response = await chrome.runtime.sendMessage({ action: 'getLogs' });
            if (response && response.logs) {
                const blob = new Blob([JSON.stringify(response.logs, null, 2)], {
                    type: 'application/json'
                });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `环境监测采集器日志_${new Date().toISOString().slice(0, 10)}.json`;
                a.click();
                URL.revokeObjectURL(url);
            }
        } catch (error) {
            console.error('Failed to export logs:', error);
            this.showMessage('导出失败: ' + error.message, 'error');
        }
    }

    selectDefaultPath() {
        const path = prompt('请输入默认保存路径:', document.getElementById('defaultSavePath').value || 'C:\\Screenshots\\');
        if (path) {
            document.getElementById('defaultSavePath').value = path;
        }
    }

    async cleanupData() {
        if (confirm('确定要清理过期数据吗？此操作不可撤销。')) {
            try {
                // 这里应该调用background script的清理方法
                this.showMessage('数据清理完成', 'success');
            } catch (error) {
                console.error('Failed to cleanup data:', error);
                this.showMessage('清理失败: ' + error.message, 'error');
            }
        }
    }

    async exportAllData() {
        try {
            const result = await chrome.storage.local.get(null);
            const blob = new Blob([JSON.stringify(result, null, 2)], {
                type: 'application/json'
            });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `环境监测采集器数据_${new Date().toISOString().slice(0, 10)}.json`;
            a.click();
            URL.revokeObjectURL(url);
        } catch (error) {
            console.error('Failed to export all data:', error);
            this.showMessage('导出失败: ' + error.message, 'error');
        }
    }

    importSettings() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = async (e) => {
            const file = e.target.files[0];
            if (file) {
                try {
                    const text = await file.text();
                    const settings = JSON.parse(text);
                    
                    await chrome.storage.local.set({ advancedSettings: settings });
                    await this.loadSettings();
                    
                    this.showMessage('配置导入成功', 'success');
                } catch (error) {
                    console.error('Failed to import settings:', error);
                    this.showMessage('导入失败: ' + error.message, 'error');
                }
            }
        };
        input.click();
    }

    async exportSettings() {
        try {
            const result = await chrome.storage.local.get(['advancedSettings', 'customRules']);
            const blob = new Blob([JSON.stringify(result, null, 2)], {
                type: 'application/json'
            });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `环境监测采集器配置_${new Date().toISOString().slice(0, 10)}.json`;
            a.click();
            URL.revokeObjectURL(url);
        } catch (error) {
            console.error('Failed to export settings:', error);
            this.showMessage('导出失败: ' + error.message, 'error');
        }
    }

    openHelp() {
        chrome.tabs.create({ url: 'https://github.com/your-repo/help' });
    }

    openFeedback() {
        chrome.tabs.create({ url: 'https://github.com/your-repo/issues' });
    }

    showMessage(message, type = 'info') {
        // 创建消息提示
        const messageDiv = document.createElement('div');
        messageDiv.className = `message message-${type}`;
        messageDiv.textContent = message;
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 6px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            animation: slideIn 0.3s ease;
            background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
        `;
        
        document.body.appendChild(messageDiv);
        
        // 3秒后自动移除
        setTimeout(() => {
            messageDiv.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => {
                document.body.removeChild(messageDiv);
            }, 300);
        }, 3000);
    }
}

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// 初始化选项管理器
const optionsManager = new OptionsManager();
